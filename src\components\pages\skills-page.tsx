'use client';

import { useState } from 'react';
import { Z<PERSON>, Star } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PageLayout } from '@/components/layout/page-layout';
import { PageContainer } from '@/components/layout/page-container';

// 模拟数据
const mockSkills = [
  {
    id: '1',
    name: 'Python编程',
    category: '编程语言',
    description: '一种简洁、易读、功能强大的编程语言，广泛应用于数据科学、人工智能、Web开发等领域。',
    level: 'intermediate',
    demandLevel: 'very-high',
    relatedCareers: ['软件工程师', '数据科学家', 'AI工程师'],
    learningResources: ['官方文档', '在线课程', '实战项目'],
    tags: ['热门', '实用', '入门友好']
  },
  {
    id: '2',
    name: '机器学习',
    category: '人工智能',
    description: '让计算机通过数据学习和改进性能的技术，是人工智能的核心技术之一。',
    level: 'advanced',
    demandLevel: 'very-high',
    relatedCareers: ['算法工程师', '数据科学家', 'AI研究员'],
    learningResources: ['学术论文', '在线课程', '开源项目'],
    tags: ['前沿', '高薪', '有挑战']
  },
  {
    id: '3',
    name: 'UI/UX设计',
    category: '设计',
    description: '用户界面和用户体验设计，关注产品的可用性、易用性和用户满意度。',
    level: 'intermediate',
    demandLevel: 'high',
    relatedCareers: ['UI设计师', 'UX设计师', '产品设计师'],
    learningResources: ['设计工具', '案例分析', '用户研究'],
    tags: ['创意', '用户导向', '视觉']
  }
];

export function SkillsPage() {
  const [skills] = useState(mockSkills);

  const getLevelText = (level: string) => {
    const levelMap = {
      'beginner': '入门',
      'intermediate': '中级',
      'advanced': '高级',
      'expert': '专家'
    };
    return levelMap[level as keyof typeof levelMap] || level;
  };

  const getDemandText = (demand: string) => {
    const demandMap = {
      'low': '低需求',
      'medium': '中等需求',
      'high': '高需求',
      'very-high': '极高需求'
    };
    return demandMap[demand as keyof typeof demandMap] || demand;
  };

  const getDifficultyStars = (level: string) => {
    const starMap = {
      'beginner': 1,
      'intermediate': 2,
      'advanced': 3,
      'expert': 4
    };
    return starMap[level as keyof typeof starMap] || 0;
  };

  return (
    <PageLayout
      backgroundPattern="gradient"
      showBreadcrumb={true}
      breadcrumbItems={[{ label: '技能库', current: true }]}
      currentPath="/skills"
    >
      <PageContainer>
        <div className="space-y-8">
          {/* 页面标题 */}
          <div className="text-center">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              技能库
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              掌握核心技能，提升职场竞争力
            </p>
          </div>

          {/* 技能列表 */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {skills.map((skill) => (
              <Card key={skill.id} className="hover-lift glass cursor-pointer">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary to-purple-600 rounded-xl flex items-center justify-center mb-3">
                      <Zap className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {skill.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  <CardTitle className="text-lg text-foreground">
                    {skill.name}
                  </CardTitle>

                  <CardDescription className="text-sm text-muted-foreground">
                    {skill.category} · {getLevelText(skill.level)}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {skill.description}
                  </p>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">学习难度</span>
                      <div className="flex items-center space-x-1">
                        {[...Array(4)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-3 h-3 ${
                              i < getDifficultyStars(skill.level)
                                ? "text-warning fill-current"
                                : "text-muted-foreground"
                            }`}
                          />
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">市场需求</span>
                      <span className="text-sm font-medium text-success">
                        {getDemandText(skill.demandLevel)}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">相关职业</span>
                      <span className="text-sm font-medium text-foreground">
                        {skill.relatedCareers.length}+
                      </span>
                    </div>
                  </div>

                  {/* 学习资源 */}
                  <div>
                    <div className="text-sm font-medium text-foreground mb-2">学习资源</div>
                    <div className="flex flex-wrap gap-1">
                      {skill.learningResources.slice(0, 3).map((resource, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-secondary text-secondary-foreground rounded-full text-xs"
                        >
                          {resource}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" className="flex-1">
                      开始学习
                    </Button>
                    <Button variant="outline" size="sm">
                      路径
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </PageContainer>
    </PageLayout>
  );
}
