import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface PageContainerProps {
  children: ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | '6xl' | '7xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

export function PageContainer({ 
  children, 
  className,
  maxWidth = '7xl',
  padding = 'lg'
}: PageContainerProps) {
  const getMaxWidthClass = () => {
    const maxWidthMap = {
      'sm': 'max-w-sm',
      'md': 'max-w-md',
      'lg': 'max-w-lg',
      'xl': 'max-w-xl',
      '2xl': 'max-w-2xl',
      '4xl': 'max-w-4xl',
      '6xl': 'max-w-6xl',
      '7xl': 'max-w-7xl',
      'full': 'max-w-full'
    };
    return maxWidthMap[maxWidth];
  };

  const getPaddingClass = () => {
    const paddingMap = {
      'none': '',
      'sm': 'px-4 py-4',
      'md': 'px-6 py-6',
      'lg': 'px-4 sm:px-6 lg:px-8 py-8',
      'xl': 'px-4 sm:px-6 lg:px-8 py-12'
    };
    return paddingMap[padding];
  };

  return (
    <div className={cn(
      'mx-auto',
      getMaxWidthClass(),
      getPaddingClass(),
      className
    )}>
      {children}
    </div>
  );
}

// 页面头部容器
interface PageHeaderProps {
  children: ReactNode;
  className?: string;
}

export function PageHeader({ children, className }: PageHeaderProps) {
  return (
    <div className={cn('text-center mb-8', className)}>
      {children}
    </div>
  );
}

// 页面内容容器
interface PageContentProps {
  children: ReactNode;
  className?: string;
}

export function PageContent({ children, className }: PageContentProps) {
  return (
    <div className={cn('space-y-8', className)}>
      {children}
    </div>
  );
}
