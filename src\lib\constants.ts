// 应用配置
export const APP_CONFIG = {
  name: '高考职业助手',
  description: '专业的高考志愿填报和职业规划平台',
  version: '1.0.0',
  author: '高考职业助手团队',
  contact: {
    email: '<EMAIL>',
    phone: '************'
  }
};

// 路由配置
export const ROUTES = {
  HOME: '/',
  ASSESSMENT: '/assessment',
  ASSESSMENT_RESULT: '/assessment/result',
  MAJORS: '/majors',
  MAJOR_DETAIL: '/majors/[id]',
  CAREERS: '/careers',
  CAREER_DETAIL: '/careers/[id]',
  SKILLS: '/skills',
  SKILL_DETAIL: '/skills/[id]',
  EMPLOYMENT: '/employment',
  UNIVERSITIES: '/universities',
  UNIVERSITY_DETAIL: '/universities/[id]',
  VOLUNTEER: '/volunteer',
  PROFILE: '/profile',
  SETTINGS: '/settings',
  SEARCH: '/search'
} as const;

// 职业类别
export const CAREER_CATEGORIES = [
  { id: 'technology', name: '科技互联网', icon: '💻' },
  { id: 'finance', name: '金融投资', icon: '💰' },
  { id: 'education', name: '教育培训', icon: '📚' },
  { id: 'healthcare', name: '医疗健康', icon: '🏥' },
  { id: 'engineering', name: '工程建筑', icon: '🏗️' },
  { id: 'media', name: '传媒广告', icon: '📺' },
  { id: 'law', name: '法律服务', icon: '⚖️' },
  { id: 'consulting', name: '咨询服务', icon: '💼' },
  { id: 'manufacturing', name: '制造业', icon: '🏭' },
  { id: 'retail', name: '零售贸易', icon: '🛍️' },
  { id: 'transportation', name: '交通物流', icon: '🚛' },
  { id: 'agriculture', name: '农林牧渔', icon: '🌾' },
  { id: 'energy', name: '能源环保', icon: '⚡' },
  { id: 'government', name: '政府机关', icon: '🏛️' },
  { id: 'nonprofit', name: '非营利组织', icon: '🤝' },
  { id: 'arts', name: '文化艺术', icon: '🎨' },
  { id: 'sports', name: '体育运动', icon: '⚽' },
  { id: 'tourism', name: '旅游酒店', icon: '🏨' },
  { id: 'food', name: '餐饮食品', icon: '🍽️' },
  { id: 'other', name: '其他行业', icon: '📋' }
];

// 专业类别
export const MAJOR_CATEGORIES = [
  { id: 'philosophy', name: '哲学', code: '01' },
  { id: 'economics', name: '经济学', code: '02' },
  { id: 'law', name: '法学', code: '03' },
  { id: 'education', name: '教育学', code: '04' },
  { id: 'literature', name: '文学', code: '05' },
  { id: 'history', name: '历史学', code: '06' },
  { id: 'science', name: '理学', code: '07' },
  { id: 'engineering', name: '工学', code: '08' },
  { id: 'agriculture', name: '农学', code: '09' },
  { id: 'medicine', name: '医学', code: '10' },
  { id: 'management', name: '管理学', code: '12' },
  { id: 'arts', name: '艺术学', code: '13' }
];

// 技能类别
export const SKILL_CATEGORIES = [
  { id: 'technical', name: '技术技能', description: '编程、设计、分析等专业技能' },
  { id: 'soft', name: '软技能', description: '沟通、领导、团队合作等通用技能' },
  { id: 'language', name: '语言技能', description: '外语、方言等语言能力' },
  { id: 'certification', name: '认证资格', description: '专业证书、执业资格等' }
];

// 教育水平
export const EDUCATION_LEVELS = [
  { id: 'high_school', name: '高中及以下' },
  { id: 'associate', name: '专科' },
  { id: 'bachelor', name: '本科' },
  { id: 'master', name: '硕士' },
  { id: 'phd', name: '博士' }
];

// 薪资范围
export const SALARY_RANGES = [
  { id: 'below_5k', name: '5K以下', min: 0, max: 5000 },
  { id: '5k_10k', name: '5K-10K', min: 5000, max: 10000 },
  { id: '10k_20k', name: '10K-20K', min: 10000, max: 20000 },
  { id: '20k_30k', name: '20K-30K', min: 20000, max: 30000 },
  { id: '30k_50k', name: '30K-50K', min: 30000, max: 50000 },
  { id: 'above_50k', name: '50K以上', min: 50000, max: Infinity }
];

// 城市等级
export const CITY_TIERS = [
  { id: 'tier1', name: '一线城市', cities: ['北京', '上海', '广州', '深圳'] },
  { id: 'tier2', name: '新一线城市', cities: ['成都', '杭州', '重庆', '武汉', '苏州', '西安', '天津', '南京', '郑州', '长沙', '东莞', '沈阳', '青岛', '合肥', '佛山'] },
  { id: 'tier3', name: '二线城市', cities: ['无锡', '宁波', '昆明', '福州', '厦门', '济南', '大连', '哈尔滨', '温州', '南昌', '长春', '泉州', '石家庄', '贵阳', '南宁'] },
  { id: 'tier4', name: '三线及以下城市', cities: [] }
];

// 大学类型
export const UNIVERSITY_TYPES = [
  { id: '985', name: '985工程', description: '国家重点建设的世界一流大学' },
  { id: '211', name: '211工程', description: '国家重点建设的高水平大学' },
  { id: 'double_first_class', name: '双一流', description: '世界一流大学和一流学科建设高校' },
  { id: 'regular', name: '普通本科', description: '普通本科院校' }
];

// 测评类型
export const ASSESSMENT_TYPES = [
  {
    id: 'personality',
    name: '性格测评',
    description: '了解你的性格特点和行为偏好',
    duration: '15-20分钟',
    questionCount: 60,
    icon: '🧠'
  },
  {
    id: 'interest',
    name: '兴趣测评',
    description: '发现你的兴趣爱好和职业倾向',
    duration: '10-15分钟',
    questionCount: 40,
    icon: '❤️'
  },
  {
    id: 'ability',
    name: '能力测评',
    description: '评估你的各项能力和潜力',
    duration: '20-25分钟',
    questionCount: 80,
    icon: '💪'
  },
  {
    id: 'comprehensive',
    name: '综合测评',
    description: '全面评估性格、兴趣和能力',
    duration: '30-40分钟',
    questionCount: 120,
    icon: '🎯'
  }
];

// 匹配度等级
export const MATCH_LEVELS = [
  { min: 90, max: 100, level: 'excellent', name: '极高匹配', color: 'text-green-600', bgColor: 'bg-green-100' },
  { min: 80, max: 89, level: 'good', name: '高度匹配', color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { min: 70, max: 79, level: 'fair', name: '中等匹配', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  { min: 60, max: 69, level: 'low', name: '较低匹配', color: 'text-orange-600', bgColor: 'bg-orange-100' },
  { min: 0, max: 59, level: 'poor', name: '不匹配', color: 'text-red-600', bgColor: 'bg-red-100' }
];

// 就业前景
export const JOB_OUTLOOKS = [
  { id: 'excellent', name: '前景极佳', description: '需求量大，增长迅速', color: 'text-green-600' },
  { id: 'good', name: '前景良好', description: '需求稳定，有增长空间', color: 'text-blue-600' },
  { id: 'average', name: '前景一般', description: '需求平稳，竞争适中', color: 'text-yellow-600' },
  { id: 'poor', name: '前景较差', description: '需求下降，竞争激烈', color: 'text-red-600' }
];

// 公司规模
export const COMPANY_SIZES = [
  { id: 'startup', name: '初创公司', description: '1-50人' },
  { id: 'small', name: '小型公司', description: '51-200人' },
  { id: 'medium', name: '中型公司', description: '201-1000人' },
  { id: 'large', name: '大型公司', description: '1001-5000人' },
  { id: 'enterprise', name: '超大型企业', description: '5000人以上' }
];

// 学习资源类型
export const RESOURCE_TYPES = [
  { id: 'course', name: '在线课程', icon: '📚' },
  { id: 'book', name: '图书资料', icon: '📖' },
  { id: 'video', name: '视频教程', icon: '🎥' },
  { id: 'article', name: '文章博客', icon: '📝' },
  { id: 'practice', name: '实践项目', icon: '🛠️' }
];

// 难度等级
export const DIFFICULTY_LEVELS = [
  { id: 'beginner', name: '入门', color: 'text-green-600' },
  { id: 'intermediate', name: '中级', color: 'text-yellow-600' },
  { id: 'advanced', name: '高级', color: 'text-red-600' },
  { id: 'expert', name: '专家', color: 'text-purple-600' }
];

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100]
};

// API配置
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || '/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
};

// 缓存配置
export const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  LONG_TTL: 60 * 60 * 1000, // 1小时
  SHORT_TTL: 60 * 1000 // 1分钟
};

// 动画配置
export const ANIMATION_CONFIG = {
  DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500
  },
  EASING: {
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out'
  }
};
