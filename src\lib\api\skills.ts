import { supabase, Skill } from '@/lib/supabase';

export interface SkillSearchParams {
  query?: string;
  category?: string[];
  level?: string[];
  demandLevel?: string[];
  tags?: string[];
  careers?: string[];
  limit?: number;
  offset?: number;
}

export class SkillsAPI {
  static async searchSkills(params: SkillSearchParams = {}): Promise<{
    data: Skill[];
    total: number;
  }> {
    let query = supabase
      .from('skills')
      .select('*', { count: 'exact' });

    // 文本搜索
    if (params.query) {
      query = query.or(`name.ilike.%${params.query}%,description.ilike.%${params.query}%,tags.cs.{${params.query}}`);
    }

    // 分类筛选
    if (params.category && params.category.length > 0) {
      query = query.in('category', params.category);
    }

    // 技能等级
    if (params.level && params.level.length > 0) {
      query = query.in('level', params.level);
    }

    // 需求程度
    if (params.demandLevel && params.demandLevel.length > 0) {
      query = query.in('demand_level', params.demandLevel);
    }

    // 相关职业筛选
    if (params.careers && params.careers.length > 0) {
      query = query.overlaps('related_careers', params.careers);
    }

    // 标签筛选
    if (params.tags && params.tags.length > 0) {
      query = query.overlaps('tags', params.tags);
    }

    // 分页
    if (params.limit) {
      query = query.limit(params.limit);
    }
    if (params.offset) {
      query = query.range(params.offset, params.offset + (params.limit || 10) - 1);
    }

    // 排序：优先显示高需求技能
    query = query.order('demand_level', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to search skills: ${error.message}`);
    }

    return {
      data: data || [],
      total: count || 0
    };
  }

  static async getSkillById(id: string): Promise<Skill | null> {
    const { data, error } = await supabase
      .from('skills')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get skill: ${error.message}`);
    }

    return data;
  }

  static async getSkillsByCategory(category: string): Promise<Skill[]> {
    const { data, error } = await supabase
      .from('skills')
      .select('*')
      .eq('category', category)
      .order('demand_level', { ascending: false });

    if (error) {
      throw new Error(`Failed to get skills by category: ${error.message}`);
    }

    return data || [];
  }

  static async getHighDemandSkills(limit: number = 10): Promise<Skill[]> {
    const { data, error } = await supabase
      .from('skills')
      .select('*')
      .eq('demand_level', 'high')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get high demand skills: ${error.message}`);
    }

    return data || [];
  }

  static async getRelatedSkills(skillId: string, limit: number = 5): Promise<Skill[]> {
    // 首先获取当前技能信息
    const currentSkill = await this.getSkillById(skillId);
    if (!currentSkill) {
      return [];
    }

    // 基于相同类别和相关职业推荐相关技能
    const { data, error } = await supabase
      .from('skills')
      .select('*')
      .neq('id', skillId)
      .or(`category.eq.${currentSkill.category},related_careers.ov.{${currentSkill.related_careers.join(',')}}`)
      .order('demand_level', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get related skills: ${error.message}`);
    }

    return data || [];
  }

  static async getSkillCategories(): Promise<Array<{ category: string; count: number }>> {
    const { data, error } = await supabase
      .from('skills')
      .select('category')
      .not('category', 'is', null);

    if (error) {
      throw new Error(`Failed to get skill categories: ${error.message}`);
    }

    // 统计每个分类的数量
    const categoryCount: Record<string, number> = {};
    data?.forEach(item => {
      categoryCount[item.category] = (categoryCount[item.category] || 0) + 1;
    });

    return Object.entries(categoryCount).map(([category, count]) => ({
      category,
      count
    }));
  }

  static async searchSkillsByCareers(careers: string[]): Promise<Skill[]> {
    if (careers.length === 0) {
      return [];
    }

    const { data, error } = await supabase
      .from('skills')
      .select('*')
      .overlaps('related_careers', careers)
      .order('demand_level', { ascending: false });

    if (error) {
      throw new Error(`Failed to search skills by careers: ${error.message}`);
    }

    return data || [];
  }

  static async getSkillLearningPath(skillId: string): Promise<any> {
    const skill = await this.getSkillById(skillId);
    return skill?.learning_path || [];
  }

  static async getSkillLearningResources(skillId: string): Promise<any> {
    const skill = await this.getSkillById(skillId);
    return skill?.learning_resources || [];
  }

  static async getSkillPrerequisites(skillId: string): Promise<string[]> {
    const skill = await this.getSkillById(skillId);
    return skill?.prerequisites || [];
  }

  static async getSkillCertifications(skillId: string): Promise<string[]> {
    const skill = await this.getSkillById(skillId);
    return skill?.certifications || [];
  }

  static async getTrendingSkills(limit: number = 10): Promise<Skill[]> {
    // 基于需求程度和创建时间排序，获取热门技能
    const { data, error } = await supabase
      .from('skills')
      .select('*')
      .in('demand_level', ['high', 'medium'])
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get trending skills: ${error.message}`);
    }

    return data || [];
  }

  static async getBeginnerSkills(limit: number = 10): Promise<Skill[]> {
    const { data, error } = await supabase
      .from('skills')
      .select('*')
      .eq('level', 'beginner')
      .order('demand_level', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get beginner skills: ${error.message}`);
    }

    return data || [];
  }

  static async getAdvancedSkills(limit: number = 10): Promise<Skill[]> {
    const { data, error } = await supabase
      .from('skills')
      .select('*')
      .in('level', ['advanced', 'expert'])
      .order('demand_level', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get advanced skills: ${error.message}`);
    }

    return data || [];
  }
}
