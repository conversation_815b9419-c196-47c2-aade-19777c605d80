'use client';

import { useState, useRef, useEffect } from 'react';
import { Search, Filter, SortAsc, SortDesc, Grid, List, X, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface SearchBarProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  onSortChange?: (sort: string) => void;
  onViewChange?: (view: 'grid' | 'list') => void;
  onFilterToggle?: () => void;
  showFilters?: boolean;
  showSort?: boolean;
  showViewToggle?: boolean;
  sortOptions?: { value: string; label: string }[];
  currentSort?: string;
  currentView?: 'grid' | 'list';
  className?: string;
}

const DEFAULT_SORT_OPTIONS = [
  { value: 'relevance', label: '相关度' },
  { value: 'name', label: '名称' },
  { value: 'popularity', label: '热门度' },
  { value: 'rating', label: '评分' },
];

export function EnhancedSearchBar({
  placeholder = '搜索...',
  onSearch,
  onSortChange,
  onViewChange,
  onFilterToggle,
  showFilters = true,
  showSort = true,
  showViewToggle = true,
  sortOptions = DEFAULT_SORT_OPTIONS,
  currentSort = 'relevance',
  currentView = 'grid',
  className
}: SearchBarProps) {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showSortMenu, setShowSortMenu] = useState(false);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSearch = (searchQuery: string = query) => {
    onSearch(searchQuery.trim());
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
    if (e.key === 'Escape') {
      setQuery('');
      onSearch('');
      inputRef.current?.blur();
    }
  };

  const handleSortChange = (sortValue: string) => {
    onSortChange?.(sortValue);
    setShowSortMenu(false);
  };

  const toggleSortOrder = () => {
    const newOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    setSortOrder(newOrder);
    onSortChange?.(`${currentSort}-${newOrder}`);
  };

  const clearSearch = () => {
    setQuery('');
    onSearch('');
    inputRef.current?.focus();
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* 主搜索栏 */}
      <div className="flex flex-col sm:flex-row gap-3">
        {/* 搜索输入框 */}
        <div className={cn(
          "relative flex-1 transition-all duration-200",
          isFocused && "scale-[1.01]"
        )}>
          <div className={cn(
            "relative bg-card rounded-xl border-2 transition-all duration-200 hover-lift",
            isFocused ? "border-primary shadow-lg" : "border-border"
          )}>
            <div className="flex items-center">
              <Search className={cn(
                "w-5 h-5 ml-4 transition-colors duration-200",
                isFocused ? "text-primary" : "text-muted-foreground"
              )} />
              <Input
                ref={inputRef}
                type="text"
                placeholder={placeholder}
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                onKeyDown={handleKeyDown}
                className="border-0 bg-transparent text-base px-3 py-4 focus-visible:ring-0 focus-visible:ring-offset-0"
              />
              
              {/* 清除按钮 */}
              {query && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="mr-2 h-8 w-8 p-0 hover-scale"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
              
              {/* 搜索按钮 */}
              <Button
                onClick={() => handleSearch()}
                className="mr-3 bg-gradient-to-r from-primary to-purple-600 hover:from-primary-hover hover:to-purple-700 hover-scale"
              >
                搜索
              </Button>
            </div>
          </div>
        </div>

        {/* 工具栏 */}
        <div className="flex items-center gap-2">
          {/* 筛选按钮 */}
          {showFilters && (
            <Button
              variant="outline"
              onClick={onFilterToggle}
              className="hover-scale glass"
            >
              <Filter className="w-4 h-4 mr-2" />
              筛选
            </Button>
          )}

          {/* 排序控制 */}
          {showSort && (
            <div className="relative">
              <div className="flex items-center">
                <Button
                  variant="outline"
                  onClick={() => setShowSortMenu(!showSortMenu)}
                  className="hover-scale glass"
                >
                  <span className="mr-2">
                    {sortOptions.find(opt => opt.value === currentSort)?.label}
                  </span>
                  <ChevronDown className={cn(
                    "w-4 h-4 transition-transform duration-200",
                    showSortMenu && "rotate-180"
                  )} />
                </Button>
                
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleSortOrder}
                  className="ml-1 hover-scale"
                >
                  {sortOrder === 'asc' ? (
                    <SortAsc className="w-4 h-4" />
                  ) : (
                    <SortDesc className="w-4 h-4" />
                  )}
                </Button>
              </div>

              {/* 排序菜单 */}
              {showSortMenu && (
                <div className="absolute top-full right-0 mt-2 w-48 bg-card rounded-lg shadow-lg border border-border z-50 animate-slide-down glass">
                  <div className="p-2">
                    {sortOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => handleSortChange(option.value)}
                        className={cn(
                          "w-full text-left px-3 py-2 rounded-md text-sm transition-colors hover:bg-accent",
                          currentSort === option.value && "bg-accent text-accent-foreground"
                        )}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 视图切换 */}
          {showViewToggle && (
            <div className="flex items-center bg-muted rounded-lg p-1">
              <Button
                variant={currentView === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewChange?.('grid')}
                className="h-8 w-8 p-0 hover-scale"
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={currentView === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewChange?.('list')}
                className="h-8 w-8 p-0 hover-scale"
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
