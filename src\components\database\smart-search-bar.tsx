'use client';

import { useState, useRef, useEffect } from 'react';
import { Search, Sparkles, Tag, X, Mic, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface SmartSearchBarProps {
  placeholder?: string;
  onSearch: (query: string, filters: Record<string, unknown>) => void;
  suggestions?: string[];
}

interface SearchTag {
  id: string;
  text: string;
  type: 'category' | 'skill' | 'salary' | 'location' | 'custom';
  color: string;
}

const PREDEFINED_TAGS: SearchTag[] = [
  { id: 'high-salary', text: '高薪就业', type: 'salary', color: 'bg-green-100 text-green-700' },
  { id: 'ai-related', text: 'AI相关', type: 'category', color: 'bg-purple-100 text-purple-700' },
  { id: 'programming', text: '编程开发', type: 'skill', color: 'bg-blue-100 text-blue-700' },
  { id: 'data-analysis', text: '数据分析', type: 'skill', color: 'bg-cyan-100 text-cyan-700' },
  { id: 'beijing', text: '北京', type: 'location', color: 'bg-orange-100 text-orange-700' },
  { id: 'shanghai', text: '上海', type: 'location', color: 'bg-orange-100 text-orange-700' },
  { id: 'hot-major', text: '热门专业', type: 'category', color: 'bg-red-100 text-red-700' },
  { id: 'new-major', text: '新兴专业', type: 'category', color: 'bg-pink-100 text-pink-700' }
];

export function SmartSearchBar({ placeholder = "智能搜索...", onSearch, suggestions = [] }: SmartSearchBarProps) {
  const [query, setQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<SearchTag[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isAIMode, setIsAIMode] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isAIMode && query.length > 2) {
      // 模拟AI智能建议
      const mockAISuggestions = [
        '基于你的输入，推荐：计算机科学与技术',
        '相关专业：人工智能、数据科学',
        '高匹配度：软件工程、网络工程'
      ];
      setAiSuggestions(mockAISuggestions);
    } else {
      setAiSuggestions([]);
    }
  }, [query, isAIMode]);

  const handleSearch = () => {
    const filters = {
      tags: selectedTags.map(tag => tag.id),
      categories: selectedTags.filter(tag => tag.type === 'category').map(tag => tag.text),
      skills: selectedTags.filter(tag => tag.type === 'skill').map(tag => tag.text),
      locations: selectedTags.filter(tag => tag.type === 'location').map(tag => tag.text)
    };
    
    onSearch(query, filters);
    setShowSuggestions(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const addTag = (tag: SearchTag) => {
    if (!selectedTags.find(t => t.id === tag.id)) {
      setSelectedTags(prev => [...prev, tag]);
    }
  };

  const removeTag = (tagId: string) => {
    setSelectedTags(prev => prev.filter(tag => tag.id !== tagId));
  };

  const createCustomTag = (text: string) => {
    const customTag: SearchTag = {
      id: `custom_${Date.now()}`,
      text,
      type: 'custom',
      color: 'bg-gray-100 text-gray-700'
    };
    addTag(customTag);
    setQuery('');
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    handleSearch();
  };

  const toggleAIMode = () => {
    setIsAIMode(!isAIMode);
    if (!isAIMode) {
      inputRef.current?.focus();
    }
  };

  return (
    <div className="relative max-w-4xl mx-auto">
      {/* 主搜索框 */}
      <div className={cn(
        "relative bg-white rounded-2xl shadow-lg border-2 transition-all duration-200",
        showSuggestions || isAIMode ? "border-blue-500 shadow-xl" : "border-gray-200"
      )}>
        {/* 已选标签 */}
        {selectedTags.length > 0 && (
          <div className="flex flex-wrap gap-2 p-4 pb-2">
            {selectedTags.map((tag) => (
              <div
                key={tag.id}
                className={cn(
                  "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",
                  tag.color
                )}
              >
                <Tag className="w-3 h-3 mr-1" />
                {tag.text}
                <button
                  onClick={() => removeTag(tag.id)}
                  className="ml-2 hover:bg-black/10 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* 搜索输入区域 */}
        <div className="flex items-center">
          <Search className="w-5 h-5 text-gray-400 ml-4" />
          <Input
            ref={inputRef}
            type="text"
            placeholder={isAIMode ? "用自然语言描述你想找的专业..." : placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setShowSuggestions(true)}
            onBlur={() => {
              // 延迟隐藏建议，以便点击建议项
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            onKeyPress={handleKeyPress}
            className={cn(
              "border-0 bg-transparent text-lg px-2 py-4 focus-visible:ring-0 focus-visible:ring-offset-0",
              isAIMode && "text-blue-600"
            )}
          />
          
          {/* 功能按钮 */}
          <div className="flex items-center space-x-2 mr-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleAIMode}
              className={cn(
                "transition-all duration-200",
                isAIMode ? "text-blue-600 bg-blue-50" : "text-gray-400 hover:text-gray-600"
              )}
            >
              <Sparkles className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-400 hover:text-gray-600"
            >
              <Mic className="w-4 h-4" />
            </Button>
            <Button
              onClick={handleSearch}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              搜索
            </Button>
          </div>
        </div>
      </div>

      {/* 搜索建议下拉框 */}
      {showSuggestions && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-xl border border-gray-200 z-50 max-h-96 overflow-y-auto">
          {/* AI智能建议 */}
          {isAIMode && aiSuggestions.length > 0 && (
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center text-sm text-blue-600 mb-3">
                <Zap className="w-4 h-4 mr-2" />
                AI智能建议
              </div>
              <div className="space-y-2">
                {aiSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left p-2 hover:bg-blue-50 rounded-lg text-sm text-gray-700 transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* 预定义标签 */}
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center text-sm text-gray-500 mb-3">
              <Tag className="w-4 h-4 mr-2" />
              快速标签
            </div>
            <div className="flex flex-wrap gap-2">
              {PREDEFINED_TAGS.map((tag) => (
                <button
                  key={tag.id}
                  onClick={() => addTag(tag)}
                  className={cn(
                    "px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 hover:scale-105",
                    tag.color,
                    selectedTags.find(t => t.id === tag.id) && "ring-2 ring-blue-500"
                  )}
                >
                  {tag.text}
                </button>
              ))}
            </div>
          </div>

          {/* 搜索建议 */}
          {suggestions.length > 0 && (
            <div className="p-4">
              <div className="flex items-center text-sm text-gray-500 mb-3">
                <Search className="w-4 h-4 mr-2" />
                热门搜索
              </div>
              <div className="space-y-1">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left p-2 hover:bg-gray-50 rounded-lg text-sm text-gray-700 transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* 自定义标签提示 */}
          {query && !isAIMode && (
            <div className="p-4 border-t border-gray-100">
              <button
                onClick={() => createCustomTag(query)}
                className="w-full text-left p-2 hover:bg-gray-50 rounded-lg text-sm text-gray-700 transition-colors flex items-center"
              >
                <Tag className="w-4 h-4 mr-2" />
                添加 &ldquo;{query}&rdquo; 为标签
              </button>
            </div>
          )}
        </div>
      )}

      {/* AI模式提示 */}
      {isAIMode && (
        <div className="mt-2 text-center">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-blue-100 text-blue-700">
            <Sparkles className="w-3 h-3 mr-1" />
            AI智能模式已开启
          </span>
        </div>
      )}
    </div>
  );
}
