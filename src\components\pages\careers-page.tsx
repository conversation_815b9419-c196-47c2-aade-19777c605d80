'use client';

import { useState } from 'react';
import { Briefcase } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PageLayout } from '@/components/layout/page-layout';
import { PageContainer } from '@/components/layout/page-container';

// 模拟数据
const mockCareers = [
  {
    id: '1',
    title: '软件工程师',
    category: '技术开发',
    description: '负责软件系统的设计、开发、测试和维护，运用编程语言和开发工具创建高质量的软件产品。',
    averageSalary: { min: 8000, max: 25000, median: 15000 },
    jobOutlook: '前景极佳',
    requiredSkills: ['Java', 'Python', 'JavaScript', '数据库', '算法'],
    workEnvironment: '办公室',
    educationLevel: '本科',
    tags: ['热门', '高薪', '技术']
  },
  {
    id: '2',
    title: '产品经理',
    category: '产品管理',
    description: '负责产品的规划、设计和管理，协调各部门资源，确保产品按时高质量交付。',
    averageSalary: { min: 10000, max: 30000, median: 18000 },
    jobOutlook: '前景良好',
    requiredSkills: ['产品设计', '数据分析', '项目管理', '用户研究', '沟通协调'],
    workEnvironment: '办公室',
    educationLevel: '本科',
    tags: ['管理', '综合', '发展好']
  },
  {
    id: '3',
    title: '数据科学家',
    category: '数据分析',
    description: '运用统计学、机器学习等方法分析大数据，为业务决策提供数据支持和洞察。',
    averageSalary: { min: 12000, max: 35000, median: 20000 },
    jobOutlook: '前景极佳',
    requiredSkills: ['Python', 'R', '机器学习', '统计学', 'SQL'],
    workEnvironment: '办公室',
    educationLevel: '硕士',
    tags: ['前沿', '高薪', '分析']
  }
];

export function CareersPage() {
  const [careers] = useState(mockCareers);

  const formatSalary = (salary: { min: number; max: number; median: number }) => {
    return `${(salary.min / 1000).toFixed(0)}K-${(salary.max / 1000).toFixed(0)}K`;
  };

  return (
    <PageLayout
      backgroundPattern="gradient"
      showBreadcrumb={true}
      breadcrumbItems={[{ label: '职业库', current: true }]}
      currentPath="/careers"
    >
      <PageContainer>
        <div className="space-y-8">
          {/* 页面标题 */}
          <div className="text-center">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              职业库
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              探索各类职业信息，了解薪资待遇和发展前景
            </p>
          </div>

          {/* 职业列表 */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {careers.map((career) => (
              <Card key={career.id} className="hover-lift glass cursor-pointer">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary to-purple-600 rounded-xl flex items-center justify-center mb-3">
                      <Briefcase className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {career.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  <CardTitle className="text-lg text-foreground">
                    {career.title}
                  </CardTitle>

                  <CardDescription className="text-sm text-muted-foreground">
                    {career.category} · {career.workEnvironment}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {career.description}
                  </p>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">薪资范围</span>
                      <span className="text-sm font-medium text-foreground">
                        {formatSalary(career.averageSalary)}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">就业前景</span>
                      <span className="text-sm font-medium text-success">
                        {career.jobOutlook}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">学历要求</span>
                      <span className="text-sm font-medium text-foreground">
                        {career.educationLevel}
                      </span>
                    </div>
                  </div>

                  {/* 核心技能 */}
                  <div>
                    <div className="text-sm font-medium text-foreground mb-2">核心技能</div>
                    <div className="flex flex-wrap gap-1">
                      {career.requiredSkills.slice(0, 3).map((skill, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-secondary text-secondary-foreground rounded-full text-xs"
                        >
                          {skill}
                        </span>
                      ))}
                      {career.requiredSkills.length > 3 && (
                        <span className="px-2 py-1 bg-muted text-muted-foreground rounded-full text-xs">
                          +{career.requiredSkills.length - 3}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" className="flex-1">
                      查看详情
                    </Button>
                    <Button variant="outline" size="sm">
                      对比
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </PageContainer>
    </PageLayout>
  );
}
