'use client';

import { useState, useEffect } from 'react';
import { Search, Brain, BookOpen, TrendingUp, ChevronRight, Star, Users, Award, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PageLayout, PageContainer, PageSection } from '@/components/layout/page-layout';
import { ArticlesAPI } from '@/lib/api/articles';
import { MajorsAPI } from '@/lib/api/majors';
import { CareersAPI } from '@/lib/api/careers';
import { SearchBar } from '@/components/home/<USER>';
import { FeatureCards } from '@/components/home/<USER>';
import { NewsSection } from '@/components/home/<USER>';
import { StatsSection } from '@/components/home/<USER>';

export function HomePage() {
  return (
    <PageLayout
      navbarVariant="transparent"
      backgroundPattern="gradient"
      showAnnouncement={true}
      announcement={{
        message: "🎉 全新AI测评系统上线，更精准的职业匹配等你体验！",
        type: "promotion",
        actionText: "立即体验",
        actionHref: "/assessment"
      }}
      currentPath="/"
    >
      <PageContainer>
        {/* 英雄区域 */}
        <PageSection className="text-center" padding="lg">
          <div className="max-w-4xl mx-auto">
            <div className="animate-fade-in">
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
                智能规划你的
                <span className="gradient-text block md:inline">
                  职业未来
                </span>
              </h1>
            </div>
            <div className="animate-slide-up">
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                基于AI技术的个性化职业测评，帮助高考生科学选择专业，规划职业发展道路
              </p>
            </div>

            {/* 搜索栏 */}
            <div className="animate-bounce-in">
              <SearchBar />
            </div>
            
            {/* 快速统计 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 animate-slide-up">
              <div className="text-center hover-scale">
                <div className="text-2xl md:text-3xl font-bold text-primary">50万+</div>
                <div className="text-sm text-muted-foreground">用户信赖</div>
              </div>
              <div className="text-center hover-scale">
                <div className="text-2xl md:text-3xl font-bold text-purple-600">1000+</div>
                <div className="text-sm text-muted-foreground">专业覆盖</div>
              </div>
              <div className="text-center hover-scale">
                <div className="text-2xl md:text-3xl font-bold text-success">5000+</div>
                <div className="text-sm text-muted-foreground">职业数据</div>
              </div>
              <div className="text-center hover-scale">
                <div className="text-2xl md:text-3xl font-bold text-warning">98%</div>
                <div className="text-sm text-muted-foreground">满意度</div>
              </div>
            </div>
          </div>
        </PageSection>

        {/* 核心功能卡片 */}
        <PageSection>
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4 animate-fade-in">三步规划你的未来</h2>
            <p className="text-lg text-muted-foreground animate-slide-up">科学的方法，个性化的建议，让选择更明智</p>
          </div>
          <div className="animate-slide-up">
            <FeatureCards />
          </div>
        </PageSection>

        {/* 数据展示区域 */}
        <PageSection background="muted">
          <StatsSection />
        </PageSection>

        {/* 今日要闻 */}
        <PageSection>
          <NewsSection />
        </PageSection>

        {/* 用户评价 */}
        <PageSection>
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4 animate-fade-in">用户好评如潮</h2>
            <p className="text-lg text-muted-foreground animate-slide-up">听听他们的成功故事</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 animate-slide-up">
            {[
              {
                name: '张同学',
                school: '北京四中',
                content: '通过AI测评发现了自己对计算机科学的兴趣，现在在清华大学学习人工智能专业，非常感谢这个平台！',
                rating: 5
              },
              {
                name: '李同学',
                school: '上海中学',
                content: '职业规划建议非常专业，帮我选择了最适合的金融专业，现在在复旦大学学习得很开心。',
                rating: 5
              },
              {
                name: '王同学',
                school: '广州一中',
                content: '就业数据分析很详细，让我对未来的职业发展有了清晰的认识，强烈推荐！',
                rating: 5
              }
            ].map((review, index) => (
              <Card key={index} className="glass hover-lift">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(review.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-warning text-warning" />
                    ))}
                  </div>
                  <p className="text-muted-foreground mb-4">"{review.content}"</p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-gradient-to-r from-primary to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                      {review.name[0]}
                    </div>
                    <div className="ml-3">
                      <div className="font-semibold text-foreground">{review.name}</div>
                      <div className="text-sm text-muted-foreground">{review.school}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </PageSection>

        {/* CTA区域 */}
        <PageSection className="text-center">
          <div className="bg-gradient-to-r from-primary to-purple-600 rounded-2xl p-8 md:p-12 text-white hover-lift">
            <div className="animate-fade-in">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                开始你的职业规划之旅
              </h2>
            </div>
            <div className="animate-slide-up">
              <p className="text-xl mb-8 opacity-90">
                立即进行AI职业测评，发现最适合你的专业和职业方向
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-bounce-in">
              <Button size="lg" variant="secondary" className="text-primary hover-scale">
                <Brain className="w-5 h-5 mr-2" />
                开始AI测评
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary hover-scale">
                <BookOpen className="w-5 h-5 mr-2" />
                浏览专业库
              </Button>
            </div>
          </div>
        </PageSection>
      </PageContainer>

    </PageLayout>
  );
}
