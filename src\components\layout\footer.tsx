'use client';

import { Sparkles, Mail, Phone, MapPin } from 'lucide-react';
import { APP_CONFIG } from '@/lib/constants';

export function Footer() {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: '产品功能',
      links: [
        { label: 'AI职业测评', href: '/assessment' },
        { label: '专业库查询', href: '/majors' },
        { label: '职业库查询', href: '/careers' },
        { label: '就业数据分析', href: '/employment' },
      ]
    },
    {
      title: '帮助支持',
      links: [
        { label: '使用指南', href: '/help/guide' },
        { label: '常见问题', href: '/help/faq' },
        { label: '联系客服', href: '/help/contact' },
        { label: '意见反馈', href: '/help/feedback' },
      ]
    },
    {
      title: '关于我们',
      links: [
        { label: '公司介绍', href: '/about' },
        { label: '隐私政策', href: '/privacy' },
        { label: '服务条款', href: '/terms' },
        { label: '加入我们', href: '/careers' },
      ]
    }
  ];

  return (
    <footer className="bg-card border-t border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* 品牌信息 */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-foreground">{APP_CONFIG.name}</h3>
                <p className="text-xs text-muted-foreground">AI智能职业规划</p>
              </div>
            </div>
            <p className="text-muted-foreground text-sm leading-relaxed">
              {APP_CONFIG.description}，为每一位学生提供科学、专业的职业规划建议。
            </p>
            
            {/* 联系信息 */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Mail className="w-4 h-4" />
                <span>{APP_CONFIG.contact.email}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Phone className="w-4 h-4" />
                <span>{APP_CONFIG.contact.phone}</span>
              </div>
            </div>
          </div>

          {/* 导航链接 */}
          {footerSections.map((section) => (
            <div key={section.title} className="space-y-4">
              <h4 className="font-semibold text-foreground">{section.title}</h4>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.href}>
                    <a
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors duration-200"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* 底部版权信息 */}
        <div className="border-t border-border mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-muted-foreground">
              &copy; {currentYear} {APP_CONFIG.name}. 保留所有权利.
            </p>
            <div className="flex items-center space-x-6">
              <a href="/privacy" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                隐私政策
              </a>
              <a href="/terms" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                服务条款
              </a>
              <span className="text-sm text-muted-foreground">
                版本 {APP_CONFIG.version}
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
