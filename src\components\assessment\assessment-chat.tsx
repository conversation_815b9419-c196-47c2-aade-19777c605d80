'use client';

import { useState, useEffect, useRef } from 'react';
import { Bot, User, Send, RotateCcw, Pause, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface AssessmentChatProps {
  assessmentType: string;
  assessmentId: string;
  onComplete: () => void;
}

interface Message {
  id: string;
  type: 'bot' | 'user';
  content: string;
  timestamp: Date;
  options?: string[];
  questionId?: string;
}

interface Question {
  id: string;
  question: string;
  type: 'single_choice' | 'multiple_choice' | 'scale' | 'text';
  options?: string[];
  category: string;
}

// 模拟问题数据
const SAMPLE_QUESTIONS: Question[] = [
  {
    id: 'q1',
    question: '在团队合作中，你更倾向于扮演什么角色？',
    type: 'single_choice',
    options: [
      '领导者，喜欢制定计划和分配任务',
      '协调者，善于沟通和解决冲突',
      '执行者，专注于完成具体任务',
      '创意者，提供新想法和解决方案'
    ],
    category: 'personality'
  },
  {
    id: 'q2',
    question: '面对压力和挑战时，你通常会？',
    type: 'single_choice',
    options: [
      '迎难而上，把挑战当作成长机会',
      '仔细分析，制定详细的应对策略',
      '寻求帮助，与他人讨论解决方案',
      '暂时回避，等待更好的时机'
    ],
    category: 'personality'
  },
  {
    id: 'q3',
    question: '你对以下哪个领域最感兴趣？',
    type: 'single_choice',
    options: [
      '科学技术和创新',
      '艺术创作和设计',
      '商业管理和经营',
      '社会服务和帮助他人'
    ],
    category: 'interest'
  }
];

export function AssessmentChat({ assessmentType, assessmentId, onComplete }: AssessmentChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [isTyping, setIsTyping] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // 初始化对话
    const welcomeMessage: Message = {
      id: 'welcome',
      type: 'bot',
      content: `你好！我是你的AI职业测评助手。接下来我会通过一系列问题来了解你的性格特点和职业倾向。请放松心情，根据第一直觉回答问题。准备好了吗？`,
      timestamp: new Date()
    };

    setMessages([welcomeMessage]);
    
    // 延迟显示第一个问题
    setTimeout(() => {
      askNextQuestion();
    }, 2000);
  }, []);

  const askNextQuestion = () => {
    if (currentQuestionIndex >= SAMPLE_QUESTIONS.length) {
      completeAssessment();
      return;
    }

    setIsTyping(true);
    
    setTimeout(() => {
      const question = SAMPLE_QUESTIONS[currentQuestionIndex];
      const questionMessage: Message = {
        id: question.id,
        type: 'bot',
        content: question.question,
        timestamp: new Date(),
        options: question.options,
        questionId: question.id
      };

      setMessages(prev => [...prev, questionMessage]);
      setIsTyping(false);
    }, 1000);
  };

  const handleAnswer = (questionId: string, answer: string) => {
    // 记录用户答案
    setAnswers(prev => ({ ...prev, [questionId]: answer }));

    // 添加用户回答消息
    const userMessage: Message = {
      id: `answer_${questionId}`,
      type: 'user',
      content: answer,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);

    // 添加确认消息
    setTimeout(() => {
      const confirmMessage: Message = {
        id: `confirm_${questionId}`,
        type: 'bot',
        content: '好的，我记录下了你的回答。',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, confirmMessage]);
      
      // 继续下一个问题
      setCurrentQuestionIndex(prev => prev + 1);
      setTimeout(() => {
        askNextQuestion();
      }, 1500);
    }, 500);
  };

  const completeAssessment = () => {
    const completionMessage: Message = {
      id: 'completion',
      type: 'bot',
      content: '太棒了！你已经完成了所有问题。我正在分析你的回答，马上为你生成个性化的职业测评报告...',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, completionMessage]);

    // 模拟分析过程
    setTimeout(() => {
      onComplete();
    }, 3000);
  };

  const handlePause = () => {
    setIsPaused(!isPaused);
  };

  const handleRestart = () => {
    setMessages([]);
    setCurrentQuestionIndex(0);
    setAnswers({});
    setIsTyping(false);
    setIsPaused(false);
    
    // 重新开始
    setTimeout(() => {
      const welcomeMessage: Message = {
        id: 'welcome_restart',
        type: 'bot',
        content: '好的，让我们重新开始测评。',
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
      setTimeout(() => askNextQuestion(), 1000);
    }, 500);
  };

  const progress = ((currentQuestionIndex) / SAMPLE_QUESTIONS.length) * 100;

  return (
    <div className="max-w-4xl mx-auto">
      {/* 进度条 */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">
            测评进度
          </span>
          <span className="text-sm text-gray-500">
            {currentQuestionIndex}/{SAMPLE_QUESTIONS.length}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-500"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex justify-end space-x-2 mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handlePause}
          className="flex items-center"
        >
          {isPaused ? <Play className="w-4 h-4 mr-2" /> : <Pause className="w-4 h-4 mr-2" />}
          {isPaused ? '继续' : '暂停'}
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRestart}
          className="flex items-center"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          重新开始
        </Button>
      </div>

      {/* 对话区域 */}
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="space-y-4 max-h-[600px] overflow-y-auto">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex items-start space-x-3",
                  message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                )}
              >
                {/* 头像 */}
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
                  message.type === 'bot' 
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600' 
                    : 'bg-gradient-to-r from-green-500 to-emerald-500'
                )}>
                  {message.type === 'bot' ? (
                    <Bot className="w-4 h-4 text-white" />
                  ) : (
                    <User className="w-4 h-4 text-white" />
                  )}
                </div>

                {/* 消息内容 */}
                <div className={cn(
                  "flex-1 max-w-md",
                  message.type === 'user' ? 'text-right' : ''
                )}>
                  <div className={cn(
                    "rounded-2xl px-4 py-3 text-sm",
                    message.type === 'bot'
                      ? 'bg-gray-100 text-gray-900'
                      : 'bg-blue-600 text-white'
                  )}>
                    {message.content}
                  </div>

                  {/* 选项按钮 */}
                  {message.options && message.questionId && !answers[message.questionId] && (
                    <div className="mt-3 space-y-2">
                      {message.options.map((option, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          className="w-full text-left justify-start hover:bg-blue-50 hover:border-blue-300"
                          onClick={() => handleAnswer(message.questionId!, option)}
                          disabled={isPaused}
                        >
                          <span className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-xs mr-3">
                            {String.fromCharCode(65 + index)}
                          </span>
                          {option}
                        </Button>
                      ))}
                    </div>
                  )}

                  <div className="text-xs text-gray-500 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}

            {/* 打字指示器 */}
            {isTyping && (
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div className="bg-gray-100 rounded-2xl px-4 py-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
