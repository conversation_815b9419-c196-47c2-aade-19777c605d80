// 用户相关类型
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  phone?: string;
  grade?: string; // 年级
  province?: string; // 省份
  city?: string; // 城市
  school?: string; // 学校
  createdAt: Date;
  updatedAt: Date;
}

// 职业测评相关类型
export interface Assessment {
  id: string;
  userId: string;
  type: 'personality' | 'interest' | 'ability' | 'comprehensive';
  status: 'pending' | 'in_progress' | 'completed';
  questions: AssessmentQuestion[];
  answers: AssessmentAnswer[];
  result?: AssessmentResult;
  createdAt: Date;
  completedAt?: Date;
}

export interface AssessmentQuestion {
  id: string;
  type: 'single_choice' | 'multiple_choice' | 'scale' | 'text';
  question: string;
  options?: string[];
  required: boolean;
  category: string;
}

export interface AssessmentAnswer {
  questionId: string;
  answer: string | string[] | number;
}

export interface AssessmentResult {
  id: string;
  assessmentId: string;
  personalityType: string;
  interests: string[];
  abilities: string[];
  recommendedCareers: Career[];
  careerMatch: CareerMatch[];
  growthPath: GrowthPath;
  summary: string;
}

// 职业相关类型
export interface Career {
  id: string;
  name: string;
  description: string;
  category: string;
  subcategory: string;
  requiredSkills: Skill[];
  requiredEducation: string[];
  averageSalary: SalaryRange;
  jobOutlook: 'excellent' | 'good' | 'average' | 'poor';
  workEnvironment: string;
  relatedMajors: Major[];
  companies: Company[];
  tags: string[];
}

export interface CareerMatch {
  career: Career;
  matchScore: number; // 0-100
  reasons: string[];
  pros: string[];
  cons: string[];
}

// 专业相关类型
export interface Major {
  id: string;
  name: string;
  code: string;
  category: string;
  subcategory: string;
  description: string;
  coreSubjects: string[];
  careerProspects: Career[];
  admissionRequirements: AdmissionRequirement[];
  universities: University[];
  employmentRate: number;
  averageStartingSalary: number;
  tags: string[];
}

// 技能相关类型
export interface Skill {
  id: string;
  name: string;
  category: 'technical' | 'soft' | 'language' | 'certification';
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  relatedCareers: string[]; // Career IDs
  learningResources: LearningResource[];
  demandLevel: 'high' | 'medium' | 'low';
}

// 学习资源类型
export interface LearningResource {
  id: string;
  title: string;
  type: 'course' | 'book' | 'video' | 'article' | 'practice';
  url: string;
  provider: string;
  duration?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  rating: number;
  price: number;
  isFree: boolean;
}

// 大学相关类型
export interface University {
  id: string;
  name: string;
  type: '985' | '211' | 'double_first_class' | 'regular';
  location: {
    province: string;
    city: string;
  };
  ranking: {
    national?: number;
    world?: number;
  };
  majors: Major[];
  admissionData: AdmissionData[];
}

export interface AdmissionData {
  year: number;
  province: string;
  majorId: string;
  minScore: number;
  avgScore: number;
  maxScore: number;
  admissionCount: number;
  applicationCount: number;
}

export interface AdmissionRequirement {
  province: string;
  year: number;
  minScore: number;
  subjectRequirements: string[];
  additionalRequirements?: string[];
}

// 就业数据类型
export interface EmploymentData {
  id: string;
  majorId?: string;
  careerId?: string;
  year: number;
  region: string;
  employmentRate: number;
  averageSalary: number;
  salaryDistribution: SalaryDistribution;
  topEmployers: Company[];
  industryDistribution: IndustryDistribution[];
}

export interface SalaryRange {
  min: number;
  max: number;
  median: number;
  currency: string;
}

export interface SalaryDistribution {
  ranges: {
    range: string;
    percentage: number;
  }[];
}

export interface Company {
  id: string;
  name: string;
  industry: string;
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  location: string;
  description: string;
  website?: string;
  logo?: string;
}

export interface IndustryDistribution {
  industry: string;
  percentage: number;
  averageSalary: number;
}

// 成长路径类型
export interface GrowthPath {
  id: string;
  title: string;
  description: string;
  stages: GrowthStage[];
  totalDuration: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface GrowthStage {
  id: string;
  title: string;
  description: string;
  duration: string;
  skills: Skill[];
  milestones: string[];
  resources: LearningResource[];
  order: number;
}

// 志愿方案类型
export interface VolunteerPlan {
  id: string;
  userId: string;
  name: string;
  year: number;
  province: string;
  category: 'science' | 'liberal_arts' | 'comprehensive';
  estimatedScore: number;
  preferences: VolunteerPreference[];
  schools: VolunteerSchool[];
  status: 'draft' | 'finalized' | 'submitted';
  createdAt: Date;
  updatedAt: Date;
}

export interface VolunteerPreference {
  type: 'location' | 'major' | 'school_type' | 'career';
  value: string;
  weight: number; // 1-10
}

export interface VolunteerSchool {
  universityId: string;
  majorId: string;
  priority: number;
  admissionProbability: 'high' | 'medium' | 'low';
  reasons: string[];
}

// 搜索相关类型
export interface SearchFilters {
  category?: string;
  location?: string;
  salaryRange?: SalaryRange;
  educationLevel?: string[];
  skills?: string[];
  tags?: string[];
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 通用类型
export interface PaginationParams {
  page: number;
  pageSize: number;
}

export interface SortParams {
  field: string;
  order: 'asc' | 'desc';
}
