'use client';

import { useState } from 'react';
import { Clock, Users, ChevronRight, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ASSESSMENT_TYPES } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface AssessmentTypeSelectorProps {
  onSelect: (type: string) => void;
}

export function AssessmentTypeSelector({ onSelect }: AssessmentTypeSelectorProps) {
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [hoveredType, setHoveredType] = useState<string | null>(null);

  const handleSelect = (typeId: string) => {
    setSelectedType(typeId);
  };

  const handleConfirm = () => {
    if (selectedType) {
      onSelect(selectedType);
    }
  };

  return (
    <div className="space-y-6">
      {/* 测评类型卡片 */}
      <div className="grid md:grid-cols-2 gap-6">
        {ASSESSMENT_TYPES.map((type) => {
          const isSelected = selectedType === type.id;
          const isHovered = hoveredType === type.id;
          
          return (
            <Card
              key={type.id}
              className={cn(
                "cursor-pointer transition-all duration-300 border-2",
                isSelected 
                  ? "border-blue-500 bg-blue-50 shadow-lg scale-105" 
                  : "border-gray-200 hover:border-blue-300 hover:shadow-md",
                isHovered && !isSelected && "scale-102"
              )}
              onClick={() => handleSelect(type.id)}
              onMouseEnter={() => setHoveredType(type.id)}
              onMouseLeave={() => setHoveredType(null)}
            >
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "w-12 h-12 rounded-xl flex items-center justify-center text-2xl transition-all duration-300",
                      isSelected 
                        ? "bg-blue-600 text-white" 
                        : "bg-gray-100 text-gray-600"
                    )}>
                      {type.icon}
                    </div>
                    <div>
                      <CardTitle className={cn(
                        "text-xl font-bold transition-colors",
                        isSelected ? "text-blue-900" : "text-gray-900"
                      )}>
                        {type.name}
                      </CardTitle>
                      <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {type.duration}
                        </div>
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          {type.questionCount}题
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* 选择指示器 */}
                  <div className={cn(
                    "w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-300",
                    isSelected 
                      ? "border-blue-500 bg-blue-500" 
                      : "border-gray-300"
                  )}>
                    {isSelected && (
                      <div className="w-2 h-2 bg-white rounded-full" />
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <CardDescription className={cn(
                  "text-base mb-4 transition-colors",
                  isSelected ? "text-blue-800" : "text-gray-600"
                )}>
                  {type.description}
                </CardDescription>
                
                {/* 特色功能 */}
                <div className="space-y-2">
                  {type.id === 'personality' && (
                    <>
                      <div className="flex items-center text-sm text-gray-600">
                        <Star className="w-4 h-4 mr-2 text-yellow-500" />
                        MBTI性格类型分析
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Star className="w-4 h-4 mr-2 text-yellow-500" />
                        职业性格匹配度
                      </div>
                    </>
                  )}
                  
                  {type.id === 'interest' && (
                    <>
                      <div className="flex items-center text-sm text-gray-600">
                        <Star className="w-4 h-4 mr-2 text-yellow-500" />
                        霍兰德兴趣类型
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Star className="w-4 h-4 mr-2 text-yellow-500" />
                        专业兴趣匹配
                      </div>
                    </>
                  )}
                  
                  {type.id === 'ability' && (
                    <>
                      <div className="flex items-center text-sm text-gray-600">
                        <Star className="w-4 h-4 mr-2 text-yellow-500" />
                        多元智能评估
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Star className="w-4 h-4 mr-2 text-yellow-500" />
                        能力发展建议
                      </div>
                    </>
                  )}
                  
                  {type.id === 'comprehensive' && (
                    <>
                      <div className="flex items-center text-sm text-gray-600">
                        <Star className="w-4 h-4 mr-2 text-yellow-500" />
                        全方位职业画像
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Star className="w-4 h-4 mr-2 text-yellow-500" />
                        个性化成长路径
                      </div>
                    </>
                  )}
                </div>
                
                {/* 推荐标签 */}
                {type.id === 'comprehensive' && (
                  <div className="mt-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-orange-500 to-red-500 text-white">
                      🔥 最受欢迎
                    </span>
                  </div>
                )}
                
                {type.id === 'personality' && (
                  <div className="mt-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                      ⭐ 新手推荐
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 确认按钮 */}
      {selectedType && (
        <div className="text-center animate-slide-up">
          <Button
            size="lg"
            onClick={handleConfirm}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-8 py-3 text-lg"
          >
            开始 {ASSESSMENT_TYPES.find(t => t.id === selectedType)?.name}
            <ChevronRight className="w-5 h-5 ml-2" />
          </Button>
          
          <p className="text-sm text-gray-500 mt-3">
            预计用时：{ASSESSMENT_TYPES.find(t => t.id === selectedType)?.duration}
          </p>
        </div>
      )}

      {/* 底部说明 */}
      <div className="bg-gray-50 rounded-lg p-6 mt-8">
        <h3 className="font-semibold text-gray-900 mb-3">测评建议</h3>
        <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <strong className="text-gray-900">首次测评用户：</strong>
            <br />
            建议从性格测评开始，了解基本的性格特点和职业倾向
          </div>
          <div>
            <strong className="text-gray-900">深度了解需求：</strong>
            <br />
            选择综合测评，获得最全面的职业规划建议
          </div>
        </div>
      </div>
    </div>
  );
}
