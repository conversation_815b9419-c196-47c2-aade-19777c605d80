'use client';

import { useState } from 'react';
import { CheckCircle, Circle, Clock, BookOpen, Target, TrendingUp, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface GrowthPathProps {
  personalityType: string;
}

const GROWTH_STAGES = [
  {
    id: 'foundation',
    title: '基础建设期',
    duration: '大学1-2年级',
    description: '建立专业基础，培养核心技能',
    milestones: [
      '完成专业核心课程学习',
      '参与1-2个实践项目',
      '建立基础的专业技能体系',
      '培养良好的学习习惯'
    ],
    skills: ['基础理论知识', '学习能力', '时间管理', '团队协作'],
    resources: [
      { type: 'course', title: '专业基础课程', provider: '学校' },
      { type: 'book', title: '专业入门书籍', provider: '推荐阅读' },
      { type: 'practice', title: '基础实践项目', provider: '课程作业' }
    ]
  },
  {
    id: 'exploration',
    title: '探索发展期',
    duration: '大学3-4年级',
    description: '深入专业领域，探索职业方向',
    milestones: [
      '完成专业实习或项目',
      '确定具体的职业发展方向',
      '建立行业人脉网络',
      '获得相关技能认证'
    ],
    skills: ['专业技能', '实践能力', '沟通技巧', '行业认知'],
    resources: [
      { type: 'practice', title: '实习项目', provider: '企业实习' },
      { type: 'course', title: '专业进阶课程', provider: '在线平台' },
      { type: 'article', title: '行业资讯', provider: '专业媒体' }
    ]
  },
  {
    id: 'establishment',
    title: '职业建立期',
    duration: '毕业后1-3年',
    description: '进入职场，建立职业基础',
    milestones: [
      '获得第一份正式工作',
      '适应职场环境和文化',
      '建立专业声誉',
      '完成关键项目或任务'
    ],
    skills: ['职场适应', '专业执行', '问题解决', '职业规划'],
    resources: [
      { type: 'course', title: '职场技能培训', provider: '企业培训' },
      { type: 'book', title: '职业发展指南', provider: '职场书籍' },
      { type: 'practice', title: '工作项目', provider: '实际工作' }
    ]
  },
  {
    id: 'advancement',
    title: '职业发展期',
    duration: '工作3-8年',
    description: '提升专业能力，寻求职业突破',
    milestones: [
      '获得晋升或更好的职位',
      '成为某个领域的专家',
      '领导团队或项目',
      '建立行业影响力'
    ],
    skills: ['领导能力', '战略思维', '创新能力', '影响力'],
    resources: [
      { type: 'course', title: '管理培训课程', provider: '商学院' },
      { type: 'practice', title: '领导项目', provider: '工作实践' },
      { type: 'article', title: '行业前沿', provider: '专业期刊' }
    ]
  }
];

export function GrowthPath({ personalityType }: GrowthPathProps) {
  const [activeStage, setActiveStage] = useState<string>('foundation');
  const [completedStages, setCompletedStages] = useState<string[]>([]);

  const toggleStageCompletion = (stageId: string) => {
    setCompletedStages(prev => 
      prev.includes(stageId) 
        ? prev.filter(id => id !== stageId)
        : [...prev, stageId]
    );
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {personalityType} 类型成长路径
        </h2>
        <p className="text-gray-600">
          基于你的性格特点，为你规划的个性化职业发展路径
        </p>
      </div>

      {/* 路径时间线 */}
      <div className="relative">
        {/* 连接线 */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300" />
        
        <div className="space-y-8">
          {GROWTH_STAGES.map((stage, index) => {
            const isActive = activeStage === stage.id;
            const isCompleted = completedStages.includes(stage.id);
            
            return (
              <div key={stage.id} className="relative flex items-start">
                {/* 时间线节点 */}
                <button
                  onClick={() => toggleStageCompletion(stage.id)}
                  className={cn(
                    "relative z-10 w-16 h-16 rounded-full border-4 flex items-center justify-center transition-all duration-300",
                    isCompleted 
                      ? "bg-green-500 border-green-500 text-white" 
                      : "bg-white border-gray-300 text-gray-400 hover:border-blue-500"
                  )}
                >
                  {isCompleted ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : (
                    <Circle className="w-6 h-6" />
                  )}
                </button>

                {/* 阶段内容 */}
                <Card
                  className={cn(
                    "ml-8 flex-1 cursor-pointer transition-all duration-300 border-2",
                    isActive 
                      ? "border-blue-500 shadow-lg" 
                      : "border-gray-200 hover:border-blue-300 hover:shadow-md",
                    isCompleted && "bg-green-50 border-green-200"
                  )}
                  onClick={() => setActiveStage(stage.id)}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className={cn(
                          "text-xl",
                          isCompleted ? "text-green-800" : "text-gray-900"
                        )}>
                          {stage.title}
                        </CardTitle>
                        <CardDescription className="flex items-center mt-1">
                          <Clock className="w-4 h-4 mr-2" />
                          {stage.duration}
                        </CardDescription>
                      </div>
                      <div className="text-sm text-gray-500">
                        第 {index + 1} 阶段
                      </div>
                    </div>
                    <p className={cn(
                      "text-sm mt-2",
                      isCompleted ? "text-green-700" : "text-gray-600"
                    )}>
                      {stage.description}
                    </p>
                  </CardHeader>

                  {isActive && (
                    <CardContent className="animate-slide-up">
                      <div className="grid md:grid-cols-2 gap-6">
                        {/* 关键里程碑 */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                            <Target className="w-4 h-4 mr-2 text-blue-600" />
                            关键里程碑
                          </h4>
                          <ul className="space-y-2">
                            {stage.milestones.map((milestone, idx) => (
                              <li key={idx} className="flex items-start text-sm text-gray-600">
                                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                                {milestone}
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* 核心技能 */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                            <TrendingUp className="w-4 h-4 mr-2 text-purple-600" />
                            核心技能
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {stage.skills.map((skill, idx) => (
                              <span 
                                key={idx}
                                className="px-3 py-1 bg-purple-100 text-purple-700 text-xs rounded-full"
                              >
                                {skill}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* 学习资源 */}
                      <div className="mt-6">
                        <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                          <BookOpen className="w-4 h-4 mr-2 text-green-600" />
                          推荐资源
                        </h4>
                        <div className="grid md:grid-cols-3 gap-4">
                          {stage.resources.map((resource, idx) => (
                            <div key={idx} className="bg-gray-50 rounded-lg p-3">
                              <div className="text-sm font-medium text-gray-900">
                                {resource.title}
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                {resource.provider}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 行动按钮 */}
                      <div className="mt-6 flex space-x-3">
                        <Button size="sm" variant="outline">
                          查看详细计划
                        </Button>
                        <Button size="sm" variant="outline">
                          寻找相关资源
                        </Button>
                      </div>
                    </CardContent>
                  )}
                </Card>
              </div>
            );
          })}
        </div>
      </div>

      {/* 个性化建议 */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-900 flex items-center">
            <Target className="w-5 h-5 mr-2" />
            针对 {personalityType} 类型的特别建议
          </CardTitle>
        </CardHeader>
        <CardContent className="text-blue-800">
          <div className="space-y-3">
            <p>
              <strong>发挥优势：</strong>
              充分利用你的创造力和人际交往能力，在团队合作和创新项目中发挥领导作用。
            </p>
            <p>
              <strong>注意事项：</strong>
              保持专注力，避免同时进行太多项目。建议制定明确的目标和时间计划。
            </p>
            <p>
              <strong>发展重点：</strong>
              在保持创新思维的同时，加强执行力和细节管理能力的培养。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 下一步行动 */}
      <div className="text-center">
        <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600">
          制定我的成长计划
          <ChevronRight className="w-5 h-5 ml-2" />
        </Button>
      </div>
    </div>
  );
}
