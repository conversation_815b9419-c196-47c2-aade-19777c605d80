'use client';

import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string }>;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
}

export function Breadcrumb({ items, className, showHome = true }: BreadcrumbProps) {
  const allItems = showHome 
    ? [{ label: '首页', href: '/', icon: Home }, ...items]
    : items;

  return (
    <nav className={cn("flex items-center space-x-1 text-sm", className)} aria-label="面包屑导航">
      <ol className="flex items-center space-x-1">
        {allItems.map((item, index) => {
          const isLast = index === allItems.length - 1;
          const Icon = item.icon;

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="w-4 h-4 text-muted-foreground mx-2" />
              )}
              
              {item.href && !isLast ? (
                <a
                  href={item.href}
                  className={cn(
                    "flex items-center hover:text-primary transition-colors duration-200 hover-scale",
                    item.current ? "text-primary font-medium" : "text-muted-foreground"
                  )}
                >
                  {Icon && <Icon className="w-4 h-4 mr-1" />}
                  {item.label}
                </a>
              ) : (
                <span
                  className={cn(
                    "flex items-center",
                    isLast ? "text-foreground font-medium" : "text-muted-foreground"
                  )}
                  aria-current={isLast ? "page" : undefined}
                >
                  {Icon && <Icon className="w-4 h-4 mr-1" />}
                  {item.label}
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

// 简化版面包屑
interface SimpleBreadcrumbProps {
  current: string;
  parent?: { label: string; href: string };
  className?: string;
}

export function SimpleBreadcrumb({ current, parent, className }: SimpleBreadcrumbProps) {
  return (
    <nav className={cn("flex items-center space-x-2 text-sm", className)}>
      <Link
        href="/"
        className="text-muted-foreground hover:text-primary transition-colors hover-scale"
      >
        <Home className="w-4 h-4" />
      </Link>
      
      {parent && (
        <>
          <ChevronRight className="w-4 h-4 text-muted-foreground" />
          <a
            href={parent.href}
            className="text-muted-foreground hover:text-primary transition-colors hover-scale"
          >
            {parent.label}
          </a>
        </>
      )}
      
      <ChevronRight className="w-4 h-4 text-muted-foreground" />
      <span className="text-foreground font-medium">{current}</span>
    </nav>
  );
}

// 自动生成面包屑的Hook
export function useBreadcrumb(pathname: string) {
  const segments = pathname.split('/').filter(Boolean);
  
  const items: BreadcrumbItem[] = segments.map((segment, index) => {
    const href = '/' + segments.slice(0, index + 1).join('/');
    const isLast = index === segments.length - 1;
    
    // 根据路径段生成标签
    const getLabel = (segment: string) => {
      const labelMap: Record<string, string> = {
        'assessment': 'AI测评',
        'majors': '专业库',
        'careers': '职业库',
        'skills': '技能库',
        'employment': '就业数据',
        'profile': '个人中心',
        'settings': '设置',
        'help': '帮助中心',
      };
      
      return labelMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
    };
    
    return {
      label: getLabel(segment),
      href: isLast ? undefined : href,
      current: isLast
    };
  });
  
  return items;
}
