'use client';

import { <PERSON>ap, <PERSON><PERSON>pen, Clock, Award, TrendingUp, Users, Star, Bookmark, ExternalLink, Target, Lightbulb } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface SkillCardProps {
  skill: {
    id: string;
    name: string;
    category: string;
    description: string;
    level?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    demandLevel?: 'low' | 'medium' | 'high' | 'very-high';
    relatedCareers?: string[];
    learningPath?: string[];
    learningResources?: string[];
    prerequisites?: string[];
    certifications?: string[];
    tags?: string[];
  };
  view?: 'grid' | 'list';
  onClick?: () => void;
  className?: string;
}

export function EnhancedSkillCard({ skill, view = 'grid', onClick, className }: SkillCardProps) {
  const getLevelColor = (level?: string) => {
    switch (level) {
      case 'beginner': return 'text-success';
      case 'intermediate': return 'text-primary';
      case 'advanced': return 'text-warning';
      case 'expert': return 'text-destructive';
      default: return 'text-muted-foreground';
    }
  };

  const getLevelBgColor = (level?: string) => {
    switch (level) {
      case 'beginner': return 'bg-success/10';
      case 'intermediate': return 'bg-primary/10';
      case 'advanced': return 'bg-warning/10';
      case 'expert': return 'bg-destructive/10';
      default: return 'bg-muted';
    }
  };

  const getLevelText = (level?: string) => {
    switch (level) {
      case 'beginner': return '入门';
      case 'intermediate': return '中级';
      case 'advanced': return '高级';
      case 'expert': return '专家';
      default: return '未知';
    }
  };

  const getDemandColor = (demand?: string) => {
    switch (demand) {
      case 'very-high': return 'text-destructive';
      case 'high': return 'text-warning';
      case 'medium': return 'text-primary';
      case 'low': return 'text-muted-foreground';
      default: return 'text-muted-foreground';
    }
  };

  const getDemandText = (demand?: string) => {
    switch (demand) {
      case 'very-high': return '极高需求';
      case 'high': return '高需求';
      case 'medium': return '中等需求';
      case 'low': return '低需求';
      default: return '需求未知';
    }
  };

  const getDifficultyStars = (level?: string) => {
    switch (level) {
      case 'beginner': return 1;
      case 'intermediate': return 2;
      case 'advanced': return 3;
      case 'expert': return 4;
      default: return 0;
    }
  };

  if (view === 'list') {
    return (
      <Card 
        className={cn(
          "hover-lift glass cursor-pointer transition-all duration-200",
          className
        )}
        onClick={onClick}
      >
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-r from-primary to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="text-lg font-semibold text-foreground truncate">
                    {skill.name}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>{skill.category}</span>
                    {skill.level && (
                      <>
                        <span>•</span>
                        <span className={cn("font-medium", getLevelColor(skill.level))}>
                          {getLevelText(skill.level)}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                {skill.description}
              </p>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {skill.level && (
                  <div className="flex items-center space-x-2">
                    <Target className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">难度</span>
                    <div className="flex items-center space-x-1">
                      {[...Array(4)].map((_, i) => (
                        <Star
                          key={i}
                          className={cn(
                            "w-3 h-3",
                            i < getDifficultyStars(skill.level)
                              ? "text-warning fill-current"
                              : "text-muted-foreground"
                          )}
                        />
                      ))}
                    </div>
                  </div>
                )}
                
                {skill.demandLevel && (
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">需求</span>
                    <span className={cn("font-medium", getDemandColor(skill.demandLevel))}>
                      {getDemandText(skill.demandLevel)}
                    </span>
                  </div>
                )}
                
                {skill.relatedCareers && skill.relatedCareers.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">相关职业</span>
                    <span className="font-medium text-foreground">
                      {skill.relatedCareers.length}+
                    </span>
                  </div>
                )}
              </div>

              {/* 学习路径预览 */}
              {skill.learningPath && skill.learningPath.length > 0 && (
                <div className="mt-4">
                  <div className="text-sm font-medium text-foreground mb-2">学习路径</div>
                  <div className="flex items-center space-x-2">
                    {skill.learningPath.slice(0, 3).map((step, index) => (
                      <div key={index} className="flex items-center">
                        <span className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium">
                          {index + 1}. {step}
                        </span>
                        {index < Math.min(skill.learningPath!.length - 1, 2) && (
                          <div className="w-4 h-0.5 bg-border mx-1"></div>
                        )}
                      </div>
                    ))}
                    {skill.learningPath.length > 3 && (
                      <span className="text-xs text-muted-foreground">
                        +{skill.learningPath.length - 3} 步骤
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2 ml-4">
              <Button variant="ghost" size="icon" className="hover-scale">
                <Bookmark className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" className="hover-scale">
                <ExternalLink className="w-4 h-4 mr-2" />
                学习
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      className={cn(
        "hover-lift glass cursor-pointer transition-all duration-200 group",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="w-12 h-12 bg-gradient-to-r from-primary to-purple-600 rounded-xl flex items-center justify-center mb-3 hover-scale">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <div className="flex items-center space-x-2">
            {skill.level && (
              <span className={cn(
                "px-2 py-1 rounded-full text-xs font-medium",
                getLevelColor(skill.level),
                getLevelBgColor(skill.level)
              )}>
                {getLevelText(skill.level)}
              </span>
            )}
            <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity hover-scale">
              <Bookmark className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        <CardTitle className="text-lg text-foreground group-hover:text-primary transition-colors">
          {skill.name}
        </CardTitle>
        
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <span>{skill.category}</span>
          {skill.demandLevel && (
            <>
              <span>•</span>
              <span className={getDemandColor(skill.demandLevel)}>
                {getDemandText(skill.demandLevel)}
              </span>
            </>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground line-clamp-3">
          {skill.description}
        </p>

        {/* 难度指示器 */}
        {skill.level && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">学习难度</span>
            </div>
            <div className="flex items-center space-x-1">
              {[...Array(4)].map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    "w-4 h-4",
                    i < getDifficultyStars(skill.level)
                      ? "text-warning fill-current"
                      : "text-muted-foreground"
                  )}
                />
              ))}
            </div>
          </div>
        )}

        {/* 相关信息 */}
        <div className="space-y-2">
          {skill.relatedCareers && skill.relatedCareers.length > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">相关职业</span>
              </div>
              <span className="text-sm font-medium text-foreground">
                {skill.relatedCareers.length}+
              </span>
            </div>
          )}
          
          {skill.learningResources && skill.learningResources.length > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <BookOpen className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">学习资源</span>
              </div>
              <span className="text-sm font-medium text-foreground">
                {skill.learningResources.length}+
              </span>
            </div>
          )}
        </div>

        {/* 标签 */}
        {skill.tags && skill.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {skill.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium"
              >
                {tag}
              </span>
            ))}
            {skill.tags.length > 3 && (
              <span className="px-2 py-1 bg-muted text-muted-foreground rounded-full text-xs">
                +{skill.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-2">
          <Button size="sm" className="flex-1 hover-scale">
            <Lightbulb className="w-4 h-4 mr-2" />
            开始学习
          </Button>
          <Button variant="outline" size="sm" className="hover-scale">
            路径
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
