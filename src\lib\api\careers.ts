import { supabase, Career } from '@/lib/supabase';

export interface CareerSearchParams {
  query?: string;
  category?: string[];
  salaryMin?: number;
  salaryMax?: number;
  jobOutlook?: string[];
  educationLevel?: string[];
  tags?: string[];
  skills?: string[];
  limit?: number;
  offset?: number;
}

export class CareersAPI {
  static async searchCareers(params: CareerSearchParams = {}): Promise<{
    data: Career[];
    total: number;
  }> {
    let query = supabase
      .from('careers')
      .select('*', { count: 'exact' });

    // 文本搜索
    if (params.query) {
      query = query.or(`title.ilike.%${params.query}%,description.ilike.%${params.query}%,tags.cs.{${params.query}}`);
    }

    // 分类筛选
    if (params.category && params.category.length > 0) {
      query = query.in('category', params.category);
    }

    // 薪资范围
    if (params.salaryMin !== undefined) {
      query = query.gte('salary_median', params.salaryMin);
    }
    if (params.salaryMax !== undefined) {
      query = query.lte('salary_median', params.salaryMax);
    }

    // 就业前景
    if (params.jobOutlook && params.jobOutlook.length > 0) {
      query = query.in('job_outlook', params.jobOutlook);
    }

    // 学历要求
    if (params.educationLevel && params.educationLevel.length > 0) {
      query = query.overlaps('education_level', params.educationLevel);
    }

    // 技能筛选
    if (params.skills && params.skills.length > 0) {
      query = query.overlaps('required_skills', params.skills);
    }

    // 标签筛选
    if (params.tags && params.tags.length > 0) {
      query = query.overlaps('tags', params.tags);
    }

    // 分页
    if (params.limit) {
      query = query.limit(params.limit);
    }
    if (params.offset) {
      query = query.range(params.offset, params.offset + (params.limit || 10) - 1);
    }

    // 排序
    query = query.order('salary_median', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to search careers: ${error.message}`);
    }

    return {
      data: data || [],
      total: count || 0
    };
  }

  static async getCareerById(id: string): Promise<Career | null> {
    const { data, error } = await supabase
      .from('careers')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get career: ${error.message}`);
    }

    return data;
  }

  static async getCareersByCategory(category: string): Promise<Career[]> {
    const { data, error } = await supabase
      .from('careers')
      .select('*')
      .eq('category', category)
      .order('salary_median', { ascending: false });

    if (error) {
      throw new Error(`Failed to get careers by category: ${error.message}`);
    }

    return data || [];
  }

  static async getHighSalaryCareers(limit: number = 10): Promise<Career[]> {
    const { data, error } = await supabase
      .from('careers')
      .select('*')
      .order('salary_median', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get high salary careers: ${error.message}`);
    }

    return data || [];
  }

  static async getRelatedCareers(careerId: string, limit: number = 5): Promise<Career[]> {
    // 首先获取当前职业信息
    const currentCareer = await this.getCareerById(careerId);
    if (!currentCareer) {
      return [];
    }

    // 基于相同类别和相似技能推荐相关职业
    const { data, error } = await supabase
      .from('careers')
      .select('*')
      .neq('id', careerId)
      .or(`category.eq.${currentCareer.category},required_skills.ov.{${currentCareer.required_skills.join(',')}}`)
      .order('salary_median', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get related careers: ${error.message}`);
    }

    return data || [];
  }

  static async getCareerCategories(): Promise<Array<{ category: string; count: number }>> {
    const { data, error } = await supabase
      .from('careers')
      .select('category')
      .not('category', 'is', null);

    if (error) {
      throw new Error(`Failed to get career categories: ${error.message}`);
    }

    // 统计每个分类的数量
    const categoryCount: Record<string, number> = {};
    data?.forEach(item => {
      categoryCount[item.category] = (categoryCount[item.category] || 0) + 1;
    });

    return Object.entries(categoryCount).map(([category, count]) => ({
      category,
      count
    }));
  }

  static async searchCareersByMajors(majors: string[]): Promise<Career[]> {
    if (majors.length === 0) {
      return [];
    }

    const { data, error } = await supabase
      .from('careers')
      .select('*')
      .overlaps('related_majors', majors)
      .order('salary_median', { ascending: false });

    if (error) {
      throw new Error(`Failed to search careers by majors: ${error.message}`);
    }

    return data || [];
  }

  static async searchCareersBySkills(skills: string[]): Promise<Career[]> {
    if (skills.length === 0) {
      return [];
    }

    const { data, error } = await supabase
      .from('careers')
      .select('*')
      .overlaps('required_skills', skills)
      .order('salary_median', { ascending: false });

    if (error) {
      throw new Error(`Failed to search careers by skills: ${error.message}`);
    }

    return data || [];
  }

  static async getCareerGrowthPath(careerId: string): Promise<string[]> {
    const career = await this.getCareerById(careerId);
    return career?.growth_path || [];
  }

  static async getCareerCompanies(careerId: string): Promise<string[]> {
    const career = await this.getCareerById(careerId);
    return career?.companies || [];
  }

  static async getTrendingCareers(limit: number = 10): Promise<Career[]> {
    // 基于就业前景和薪资综合排序
    const { data, error } = await supabase
      .from('careers')
      .select('*')
      .in('job_outlook', ['excellent', 'good'])
      .order('salary_median', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get trending careers: ${error.message}`);
    }

    return data || [];
  }
}
