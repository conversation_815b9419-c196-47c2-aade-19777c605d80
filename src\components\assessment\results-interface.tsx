'use client';

import { useState } from 'react';
import { Sparkles, TrendingUp, DollarSign, MapPin, Clock, Users, Target, BookOpen, Briefcase, Star, Download, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface CareerMatch {
  id: string;
  title: string;
  match: number;
  description: string;
  salary: string;
  growth: string;
  skills: string[];
  education: string;
  workStyle: string;
  pros: string[];
  cons: string[];
}

const mockResults: CareerMatch[] = [
  {
    id: '1',
    title: '软件工程师',
    match: 95,
    description: '设计、开发和维护软件系统，解决复杂的技术问题',
    salary: '15-35万',
    growth: '高增长',
    skills: ['编程', '逻辑思维', '问题解决', '团队协作'],
    education: '本科及以上',
    workStyle: '团队协作 + 独立思考',
    pros: ['薪资待遇好', '发展前景广阔', '技能可迁移'],
    cons: ['需要持续学习', '工作压力较大']
  },
  {
    id: '2',
    title: '产品经理',
    match: 88,
    description: '负责产品规划、设计和推广，协调各部门资源',
    salary: '20-50万',
    growth: '稳定增长',
    skills: ['沟通协调', '市场分析', '项目管理', '用户洞察'],
    education: '本科及以上',
    workStyle: '跨部门协作',
    pros: ['综合能力提升', '职业发展路径清晰', '影响力大'],
    cons: ['责任压力大', '需要平衡多方需求']
  },
  {
    id: '3',
    title: 'UI/UX设计师',
    match: 82,
    description: '设计用户界面和用户体验，提升产品的易用性和美观性',
    salary: '12-30万',
    growth: '中等增长',
    skills: ['设计思维', '审美能力', '用户研究', '工具使用'],
    education: '专科及以上',
    workStyle: '创意工作 + 团队协作',
    pros: ['创意性强', '成就感高', '行业需求大'],
    cons: ['主观性强', '需要不断创新']
  }
];

export function ResultsInterface() {
  const [selectedCareer, setSelectedCareer] = useState<string>(mockResults[0].id);

  const selectedCareerData = mockResults.find(career => career.id === selectedCareer);

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 结果标题 */}
      <div className="text-center animate-fade-in">
        <div className="w-16 h-16 bg-gradient-to-r from-primary to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 hover-lift">
          <Sparkles className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-3xl font-bold text-foreground mb-4">
          你的职业画像已生成！
        </h2>
        <p className="text-lg text-muted-foreground">
          基于你的回答，我们为你推荐了以下职业方向
        </p>
      </div>

      {/* 职业匹配列表 */}
      <div className="grid lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1 space-y-4">
          <h3 className="text-lg font-semibold text-foreground mb-4">推荐职业</h3>
          {mockResults.map((career, index) => (
            <Card
              key={career.id}
              className={cn(
                "cursor-pointer transition-all duration-200 hover-lift",
                selectedCareer === career.id 
                  ? "border-primary shadow-lg bg-primary/5" 
                  : "hover:border-primary/50"
              )}
              onClick={() => setSelectedCareer(career.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-foreground">{career.title}</h4>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-warning fill-current" />
                    <span className="text-sm font-medium text-foreground">{career.match}%</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3">{career.description}</p>
                
                {/* 匹配度条 */}
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-primary to-purple-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${career.match}%` }}
                  ></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 详细信息 */}
        <div className="lg:col-span-2">
          {selectedCareerData && (
            <Card className="glass animate-slide-left">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl text-foreground">
                    {selectedCareerData.title}
                  </CardTitle>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" className="hover-scale">
                      <Share2 className="w-4 h-4 mr-2" />
                      分享
                    </Button>
                    <Button variant="outline" size="sm" className="hover-scale">
                      <Download className="w-4 h-4 mr-2" />
                      下载
                    </Button>
                  </div>
                </div>
                <p className="text-muted-foreground">{selectedCareerData.description}</p>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* 关键信息 */}
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <DollarSign className="w-5 h-5 text-success" />
                    <div>
                      <div className="text-sm text-muted-foreground">薪资范围</div>
                      <div className="font-semibold text-foreground">{selectedCareerData.salary}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <TrendingUp className="w-5 h-5 text-primary" />
                    <div>
                      <div className="text-sm text-muted-foreground">发展前景</div>
                      <div className="font-semibold text-foreground">{selectedCareerData.growth}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <BookOpen className="w-5 h-5 text-purple-500" />
                    <div>
                      <div className="text-sm text-muted-foreground">学历要求</div>
                      <div className="font-semibold text-foreground">{selectedCareerData.education}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <Users className="w-5 h-5 text-orange-500" />
                    <div>
                      <div className="text-sm text-muted-foreground">工作方式</div>
                      <div className="font-semibold text-foreground">{selectedCareerData.workStyle}</div>
                    </div>
                  </div>
                </div>

                {/* 核心技能 */}
                <div>
                  <h4 className="font-semibold text-foreground mb-3 flex items-center">
                    <Target className="w-4 h-4 mr-2" />
                    核心技能
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedCareerData.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                {/* 优缺点分析 */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-success mb-3">职业优势</h4>
                    <ul className="space-y-2">
                      {selectedCareerData.pros.map((pro, index) => (
                        <li key={index} className="flex items-center text-sm text-muted-foreground">
                          <div className="w-2 h-2 bg-success rounded-full mr-3"></div>
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-warning mb-3">需要考虑</h4>
                    <ul className="space-y-2">
                      {selectedCareerData.cons.map((con, index) => (
                        <li key={index} className="flex items-center text-sm text-muted-foreground">
                          <div className="w-2 h-2 bg-warning rounded-full mr-3"></div>
                          {con}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* 行动建议 */}
                <div className="bg-gradient-to-r from-primary/10 to-purple-600/10 p-4 rounded-lg">
                  <h4 className="font-semibold text-foreground mb-2">💡 下一步建议</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• 深入了解该职业的具体工作内容和发展路径</li>
                    <li>• 寻找相关的实习或项目经验机会</li>
                    <li>• 关注行业动态和技能要求变化</li>
                    <li>• 与从业者交流，获取第一手信息</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-center space-x-4 animate-bounce-in">
        <Button variant="outline" className="hover-scale">
          <Briefcase className="w-4 h-4 mr-2" />
          查看更多职业
        </Button>
        <Button className="bg-gradient-to-r from-primary to-purple-600 hover-scale">
          <BookOpen className="w-4 h-4 mr-2" />
          探索相关专业
        </Button>
      </div>
    </div>
  );
}
