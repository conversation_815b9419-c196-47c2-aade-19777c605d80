'use client';

import { useState, useRef, useEffect } from 'react';
import { Search, Mic, Camera, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

const TRENDING_SEARCHES = [
  '计算机科学与技术',
  '人工智能',
  '金融学',
  '临床医学',
  '软件工程',
  '数据科学',
  '心理学',
  '建筑学'
];

const SEARCH_SUGGESTIONS = [
  { type: 'major', text: '计算机科学与技术专业', category: '专业' },
  { type: 'career', text: '软件工程师', category: '职业' },
  { type: 'university', text: '清华大学', category: '院校' },
  { type: 'skill', text: 'Python编程', category: '技能' },
];

export function SearchBar() {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState(SEARCH_SUGGESTIONS);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (query.length > 0) {
      // 模拟搜索建议API调用
      const filtered = SEARCH_SUGGESTIONS.filter(item =>
        item.text.toLowerCase().includes(query.toLowerCase())
      );
      setSuggestions(filtered);
      setShowSuggestions(true);
    } else {
      setSuggestions(SEARCH_SUGGESTIONS);
      setShowSuggestions(isFocused);
    }
  }, [query, isFocused]);

  const handleSearch = (searchQuery: string = query) => {
    if (searchQuery.trim()) {
      console.log('搜索:', searchQuery);
      // 这里会跳转到搜索结果页面
      setShowSuggestions(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
    if (e.key === 'Escape') {
      setShowSuggestions(false);
      inputRef.current?.blur();
    }
  };

  const handleSuggestionClick = (suggestion: typeof SEARCH_SUGGESTIONS[0]) => {
    setQuery(suggestion.text);
    handleSearch(suggestion.text);
  };

  const handleTrendingClick = (trend: string) => {
    setQuery(trend);
    handleSearch(trend);
  };

  return (
    <div className="relative max-w-2xl mx-auto">
      {/* 搜索输入框 */}
      <div className={cn(
        "relative bg-card rounded-2xl shadow-lg border-2 transition-all duration-300 hover-lift",
        isFocused ? "border-primary shadow-xl scale-[1.02]" : "border-border"
      )}>
        <div className="flex items-center">
          <Search className={cn(
            "w-5 h-5 ml-4 transition-colors duration-200",
            isFocused ? "text-primary" : "text-muted-foreground"
          )} />
          <Input
            ref={inputRef}
            type="text"
            placeholder="搜索专业、职业、院校或技能..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => {
              setIsFocused(true);
              setShowSuggestions(true);
            }}
            onBlur={() => {
              setIsFocused(false);
              // 延迟隐藏建议，以便点击建议项
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            onKeyDown={handleKeyDown}
            className="border-0 bg-transparent text-lg px-2 py-4 focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-muted-foreground"
          />

          {/* 语音搜索和拍照搜索按钮 */}
          <div className="flex items-center space-x-2 mr-4">
            <Button
              variant="ghost"
              size="sm"
              className="text-muted-foreground hover:text-foreground hover-scale"
              onClick={() => console.log('语音搜索')}
            >
              <Mic className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-muted-foreground hover:text-foreground hover-scale"
              onClick={() => console.log('拍照搜索')}
            >
              <Camera className="w-4 h-4" />
            </Button>
            <Button
              onClick={() => handleSearch()}
              className="bg-gradient-to-r from-primary to-purple-600 hover:from-primary-hover hover:to-purple-700 hover-scale"
            >
              搜索
            </Button>
          </div>
        </div>
      </div>

      {/* 搜索建议下拉框 */}
      {showSuggestions && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-card rounded-xl shadow-xl border border-border z-50 max-h-96 overflow-y-auto animate-slide-down glass">
          {query.length === 0 && (
            <>
              {/* 热门搜索 */}
              <div className="p-4 border-b border-border">
                <div className="flex items-center text-sm text-muted-foreground mb-3">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  热门搜索
                </div>
                <div className="flex flex-wrap gap-2">
                  {TRENDING_SEARCHES.map((trend, index) => (
                    <button
                      key={index}
                      onClick={() => handleTrendingClick(trend)}
                      className="px-3 py-1 bg-muted hover:bg-accent rounded-full text-sm text-foreground transition-all duration-200 hover-scale"
                    >
                      {trend}
                    </button>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* 搜索建议 */}
          <div className="p-2">
            {suggestions.length > 0 ? (
              suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full flex items-center justify-between p-3 hover:bg-accent rounded-lg text-left transition-all duration-200 hover-scale"
                >
                  <div className="flex items-center">
                    <Search className="w-4 h-4 text-muted-foreground mr-3" />
                    <span className="text-foreground">{suggestion.text}</span>
                  </div>
                  <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                    {suggestion.category}
                  </span>
                </button>
              ))
            ) : (
              <div className="p-4 text-center text-muted-foreground">
                没有找到相关建议
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
