'use client';

import { BookO<PERSON>, Briefcase, Zap, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface LinkedDatabaseNavProps {
  currentPage: 'majors' | 'careers' | 'skills';
}

const DATABASE_LINKS = [
  {
    id: 'majors',
    name: '专业库',
    icon: BookOpen,
    href: '/majors',
    description: '1200+专业详解',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100'
  },
  {
    id: 'careers',
    name: '职业库',
    icon: Briefcase,
    href: '/careers',
    description: '5000+职业数据',
    color: 'text-purple-600',
    bgColor: 'bg-purple-100'
  },
  {
    id: 'skills',
    name: '技能库',
    icon: Zap,
    href: '/skills',
    description: '技能学习路径',
    color: 'text-green-600',
    bgColor: 'bg-green-100'
  }
];

export function LinkedDatabaseNav({ currentPage }: LinkedDatabaseNavProps) {
  return (
    <div className="flex items-center space-x-1">
      {DATABASE_LINKS.map((link, index) => {
        const Icon = link.icon;
        const isActive = currentPage === link.id;
        const isCurrent = currentPage === link.id;
        
        return (
          <div key={link.id} className="flex items-center">
            <Button
              variant={isActive ? "default" : "ghost"}
              size="sm"
              onClick={() => !isCurrent && (window.location.href = link.href)}
              className={cn(
                "flex items-center space-x-2 transition-all duration-200",
                isActive 
                  ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg" 
                  : "hover:bg-gray-100",
                isCurrent && "cursor-default"
              )}
            >
              <Icon className="w-4 h-4" />
              <div className="text-left">
                <div className="font-medium">{link.name}</div>
                <div className={cn(
                  "text-xs",
                  isActive ? "text-white/80" : "text-gray-500"
                )}>
                  {link.description}
                </div>
              </div>
            </Button>
            
            {index < DATABASE_LINKS.length - 1 && (
              <ArrowRight className="w-4 h-4 text-gray-400 mx-2" />
            )}
          </div>
        );
      })}
    </div>
  );
}
