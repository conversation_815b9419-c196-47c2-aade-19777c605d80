@import "tailwindcss";

:root {
  /* 基础颜色 */
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;

  /* 主色调 - 现代蓝紫渐变 */
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --primary-hover: #2563eb;

  /* 次要颜色 */
  --secondary: #f8fafc;
  --secondary-foreground: #475569;
  --secondary-hover: #f1f5f9;

  /* 静音色 */
  --muted: #f8fafc;
  --muted-foreground: #64748b;

  /* 强调色 */
  --accent: #f1f5f9;
  --accent-foreground: #475569;
  --accent-hover: #e2e8f0;

  /* 状态颜色 */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --success: #10b981;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;

  /* 边框和输入 */
  --border: #e2e8f0;
  --input: #f8fafc;
  --ring: #3b82f6;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* 圆角 */
  --radius: 0.75rem;
  --radius-sm: 0.5rem;
  --radius-lg: 1rem;

  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary-hover: var(--primary-hover);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary-hover: var(--secondary-hover);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent-hover: var(--accent-hover);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: var(--radius);
  --radius-sm: var(--radius-sm);
  --radius-lg: var(--radius-lg);
}

.dark {
  /* 基础颜色 - 深色主题 */
  --background: #0a0a0a;
  --foreground: #f8fafc;
  --card: #111827;
  --card-foreground: #f8fafc;
  --popover: #111827;
  --popover-foreground: #f8fafc;

  /* 主色调 - 在暗色背景下更亮 */
  --primary: #60a5fa;
  --primary-foreground: #0f172a;
  --primary-hover: #3b82f6;

  /* 次要颜色 */
  --secondary: #1e293b;
  --secondary-foreground: #cbd5e1;
  --secondary-hover: #334155;

  /* 静音色 */
  --muted: #1e293b;
  --muted-foreground: #94a3b8;

  /* 强调色 */
  --accent: #1e293b;
  --accent-foreground: #cbd5e1;
  --accent-hover: #334155;

  /* 状态颜色 */
  --destructive: #f87171;
  --destructive-foreground: #0f172a;
  --success: #34d399;
  --success-foreground: #0f172a;
  --warning: #fbbf24;
  --warning-foreground: #0f172a;

  /* 边框和输入 */
  --border: #374151;
  --input: #1f2937;
  --ring: #60a5fa;

  /* 阴影 - 暗色主题下的阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);

  /* 渐变 - 暗色主题 */
  --gradient-primary: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  --gradient-secondary: linear-gradient(135deg, #1e293b 0%, #374151 100%);
}

@media (prefers-color-scheme: dark) {
  :root {
    /* 基础颜色 - 深色主题 */
    --background: #0a0a0a;
    --foreground: #f8fafc;
    --card: #111827;
    --card-foreground: #f8fafc;
    --popover: #111827;
    --popover-foreground: #f8fafc;

    /* 主色调 - 在暗色背景下更亮 */
    --primary: #60a5fa;
    --primary-foreground: #0f172a;
    --primary-hover: #3b82f6;

    /* 次要颜色 */
    --secondary: #1e293b;
    --secondary-foreground: #cbd5e1;
    --secondary-hover: #334155;

    /* 静音色 */
    --muted: #1e293b;
    --muted-foreground: #94a3b8;

    /* 强调色 */
    --accent: #1e293b;
    --accent-foreground: #cbd5e1;
    --accent-hover: #334155;

    /* 状态颜色 */
    --destructive: #f87171;
    --destructive-foreground: #0f172a;
    --success: #34d399;
    --success-foreground: #0f172a;
    --warning: #fbbf24;
    --warning-foreground: #0f172a;

    /* 边框和输入 */
    --border: #374151;
    --input: #1f2937;
    --ring: #60a5fa;

    /* 阴影 - 暗色主题下的阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);

    /* 渐变 - 暗色主题 */
    --gradient-primary: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
    --gradient-secondary: linear-gradient(135deg, #1e293b 0%, #374151 100%);
  }
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 平滑过渡 */
*,
*::before,
*::after {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* 现代化动画类 */
.animate-fade-in {
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-up {
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-down {
  animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-left {
  animation: slideLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-right {
  animation: slideRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in {
  animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-out {
  animation: scaleOut 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

/* 关键帧动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 微交互效果 */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(17, 24, 39, 0.8);
  border: 1px solid rgba(55, 65, 81, 0.3);
}

/* 渐变文字 */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
