'use client';

import { useState, useRef, useEffect } from 'react';
import { ArrowLeft, Brain, MessageCircle, Sparkles, Bot, User, Send, RefreshCw, Bookmark, BarChart3, BookmarkCheck, TrendingUp, DollarSign, Lightbulb, Target, CheckCircle, Clock, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageLayout, PageContainer, PageHeader } from '@/components/layout/page-layout';
import { ConversationInterface } from '@/components/assessment/conversation-interface';
import { ResultsInterface } from '@/components/assessment/results-interface';
import { cn } from '@/lib/utils';

type AssessmentStep = 'intro' | 'conversation' | 'results';

export function AssessmentPage() {
  const [currentStep, setCurrentStep] = useState<AssessmentStep>('intro');
  const [sessionId, setSessionId] = useState<string | null>(null);

  const handleStartConversation = () => {
    setCurrentStep('conversation');
    setSessionId(`session_${Date.now()}`);
  };

  const handleShowResults = () => {
    setCurrentStep('results');
  };

  const handleRestart = () => {
    setCurrentStep('intro');
    setSessionId(null);
  };

  const renderStepIndicator = () => {
    const steps = [
      { id: 'intro', name: '开始测评', icon: Play, description: '了解测评流程' },
      { id: 'conversation', name: 'AI对话', icon: Brain, description: '智能问答分析' },
      { id: 'results', name: '职业画像', icon: Sparkles, description: '获取专业建议' }
    ];

    return (
      <div className="mb-12">
        <div className="flex items-center justify-center">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = currentStep === step.id;
            const isCompleted = steps.findIndex(s => s.id === currentStep) > index;

            return (
              <div key={step.id} className="flex items-center">
                <div className="text-center">
                  <div className={cn(
                    "relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 hover-scale",
                    isActive
                      ? 'bg-primary border-primary text-white shadow-lg'
                      : isCompleted
                        ? 'bg-success border-success text-white'
                        : 'bg-card border-border text-muted-foreground'
                  )}>
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : (
                      <Icon className="w-6 h-6" />
                    )}
                    {isActive && (
                      <div className="absolute inset-0 rounded-full bg-primary/20 animate-pulse"></div>
                    )}
                  </div>
                  <div className="mt-3">
                    <div className={cn(
                      "text-sm font-medium transition-colors duration-200",
                      isActive ? 'text-primary' : isCompleted ? 'text-success' : 'text-muted-foreground'
                    )}>
                      {step.name}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {step.description}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={cn(
                    "w-16 h-0.5 mx-6 transition-all duration-500 relative",
                    isCompleted ? 'bg-success' : 'bg-border'
                  )}>
                    {isCompleted && (
                      <div className="absolute inset-0 bg-success animate-shimmer"></div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <PageLayout backgroundPattern="gradient">
      <PageContainer maxWidth="6xl">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-primary to-purple-600 rounded-2xl flex items-center justify-center shadow-lg hover-lift">
              <Brain className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-foreground mb-4 animate-fade-in">
            AI职业测评
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto animate-slide-up">
            通过智能对话，发现最适合你的职业方向
          </p>
        </div>

        {/* 步骤指示器 */}
        {renderStepIndicator()}

        {/* 内容区域 */}
        <div className="min-h-[600px]">
          {currentStep === 'intro' && (
            <div className="animate-fade-in">
              {/* 特色介绍 */}
              <div className="grid md:grid-cols-3 gap-6 mb-12">
                <Card className="text-center glass hover-lift">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary to-cyan-500 rounded-xl flex items-center justify-center mx-auto mb-4 hover-scale">
                      <MessageCircle className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-semibold text-foreground mb-2">自然对话</h3>
                    <p className="text-sm text-muted-foreground">
                      像和朋友聊天一样，自然地分享你的想法和偏好
                    </p>
                  </CardContent>
                </Card>

                <Card className="text-center glass hover-lift">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-4 hover-scale">
                      <Sparkles className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-semibold text-foreground mb-2">即时推荐</h3>
                    <p className="text-sm text-muted-foreground">
                      AI实时分析你的输入，即时生成个性化职业画像
                    </p>
                  </CardContent>
                </Card>

                <Card className="text-center glass hover-lift">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-success to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-4 hover-scale">
                      <Target className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-semibold text-foreground mb-2">精准匹配</h3>
                    <p className="text-sm text-muted-foreground">
                      基于大数据分析，为你匹配最适合的职业方向
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* 开始按钮 */}
              <div className="text-center animate-bounce-in">
                <Button
                  size="lg"
                  onClick={handleStartConversation}
                  className="bg-gradient-to-r from-primary to-purple-600 hover:from-primary-hover hover:to-purple-700 px-8 py-4 text-lg hover-lift shadow-lg"
                >
                  <MessageCircle className="w-5 h-5 mr-2" />
                  开始AI对话
                </Button>
                <p className="text-sm text-muted-foreground mt-3">
                  <Clock className="w-4 h-4 inline mr-1" />
                  预计用时：5-10分钟，随时可以暂停
                </p>
              </div>

              {/* 说明 */}
              <Card className="mt-12 glass">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-foreground mb-4 text-center">
                    💡 这次对话，你将会...
                  </h3>
                  <div className="grid md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                        分享你的兴趣爱好和擅长领域
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                        表达你的职业期望和理想城市
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                        获得3-5个匹配的职业画像
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                        对比不同职业的优劣势
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                        收藏感兴趣的职业方向
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                        查看详细的成长路线图
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {currentStep === 'conversation' && sessionId && (
            <div className="animate-fade-in">
              <ConversationInterface onComplete={handleShowResults} />
            </div>
          )}

          {currentStep === 'results' && sessionId && (
            <div className="animate-fade-in">
              <ResultsInterface />
            </div>
          )}
        </div>
      </PageContainer>
    </PageLayout>
  );
}


