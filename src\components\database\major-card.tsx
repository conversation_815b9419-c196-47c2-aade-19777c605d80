'use client';

import { useState } from 'react';
import { Book<PERSON><PERSON>, TrendingUp, Users, DollarSign, MapPin, Award, ChevronRight, Heart, BarChart3, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface Major {
  id: string;
  name: string;
  code: string;
  category: string;
  subcategory: string;
  description: string;
  coreSubjects: string[];
  employmentRate: number;
  averageStartingSalary: number;
  jobOutlook: 'excellent' | 'good' | 'average' | 'poor';
  relatedCareers: string[];
  relatedSkills: string[];
  universities: string[];
  admissionScore: { min: number; avg: number; max: number };
  tags: string[];
}

interface MajorCardProps {
  major: Major;
  viewMode: 'grid' | 'list';
  onViewDetails: (id: string) => void;
  onCompare: (id: string) => void;
}

const JO<PERSON>_OUTLOOK_CONFIG = {
  excellent: { text: '前景极佳', color: 'text-green-600', bgColor: 'bg-green-100' },
  good: { text: '前景良好', color: 'text-blue-600', bgColor: 'bg-blue-100' },
  average: { text: '前景一般', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  poor: { text: '前景较差', color: 'text-red-600', bgColor: 'bg-red-100' }
};

export function MajorCard({ major, viewMode, onViewDetails, onCompare }: MajorCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const outlookConfig = JOB_OUTLOOK_CONFIG[major.jobOutlook];

  const formatSalary = (salary: number) => {
    if (salary >= 10000) {
      return `${(salary / 10000).toFixed(1)}万`;
    }
    return `${salary}`;
  };

  if (viewMode === 'list') {
    return (
      <Card className="transition-all duration-300 hover:shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h3 className="text-xl font-bold text-gray-900">{major.name}</h3>
                <span className="text-sm text-gray-500">({major.code})</span>
                <div className={cn(
                  "px-2 py-1 rounded-full text-xs font-medium",
                  outlookConfig.color,
                  outlookConfig.bgColor
                )}>
                  {outlookConfig.text}
                </div>
              </div>
              
              <p className="text-gray-600 mb-4 line-clamp-2">{major.description}</p>
              
              <div className="grid grid-cols-4 gap-6 mb-4">
                <div className="flex items-center">
                  <TrendingUp className="w-4 h-4 text-green-600 mr-2" />
                  <div>
                    <div className="text-sm font-semibold text-gray-900">{major.employmentRate}%</div>
                    <div className="text-xs text-gray-500">就业率</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 text-blue-600 mr-2" />
                  <div>
                    <div className="text-sm font-semibold text-gray-900">{formatSalary(major.averageStartingSalary)}</div>
                    <div className="text-xs text-gray-500">起薪</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Users className="w-4 h-4 text-purple-600 mr-2" />
                  <div>
                    <div className="text-sm font-semibold text-gray-900">{major.relatedCareers.length}</div>
                    <div className="text-xs text-gray-500">相关职业</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Award className="w-4 h-4 text-orange-600 mr-2" />
                  <div>
                    <div className="text-sm font-semibold text-gray-900">{major.admissionScore.avg}</div>
                    <div className="text-xs text-gray-500">平均分</div>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {major.tags.slice(0, 4).map((tag, index) => (
                  <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="flex items-center space-x-2 ml-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsLiked(!isLiked)}
                className={cn(
                  "p-2",
                  isLiked ? "text-red-500" : "text-gray-400"
                )}
              >
                <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
              </Button>
              <Button variant="outline" size="sm" onClick={() => onCompare(major.id)}>
                <BarChart3 className="w-4 h-4 mr-2" />
                对比
              </Button>
              <Button size="sm" onClick={() => onViewDetails(major.id)}>
                查看详情
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-300 hover:shadow-lg border-0 bg-white/80 backdrop-blur-sm group">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <CardTitle className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                {major.name}
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsLiked(!isLiked)}
                className={cn(
                  "p-1",
                  isLiked ? "text-red-500" : "text-gray-400"
                )}
              >
                <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
              </Button>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-sm text-gray-500">{major.code}</span>
              <span className="text-sm text-gray-400">•</span>
              <span className="text-sm text-gray-500">{major.subcategory}</span>
            </div>
            <div className={cn(
              "inline-flex px-2 py-1 rounded-full text-xs font-medium",
              outlookConfig.color,
              outlookConfig.bgColor
            )}>
              {outlookConfig.text}
            </div>
          </div>
        </div>
        
        <CardDescription className="text-gray-600 line-clamp-3">
          {major.description}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 关键指标 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center">
            <TrendingUp className="w-4 h-4 text-green-600 mr-2" />
            <div>
              <div className="text-sm font-semibold text-gray-900">{major.employmentRate}%</div>
              <div className="text-xs text-gray-500">就业率</div>
            </div>
          </div>
          
          <div className="flex items-center">
            <DollarSign className="w-4 h-4 text-blue-600 mr-2" />
            <div>
              <div className="text-sm font-semibold text-gray-900">{formatSalary(major.averageStartingSalary)}</div>
              <div className="text-xs text-gray-500">起薪</div>
            </div>
          </div>
        </div>

        {/* 核心课程预览 */}
        <div>
          <h4 className="text-sm font-semibold text-gray-900 mb-2">核心课程</h4>
          <div className="flex flex-wrap gap-1">
            {major.coreSubjects.slice(0, 3).map((subject, index) => (
              <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                {subject}
              </span>
            ))}
            {major.coreSubjects.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded">
                +{major.coreSubjects.length - 3}
              </span>
            )}
          </div>
        </div>

        {/* 相关职业预览 */}
        <div>
          <h4 className="text-sm font-semibold text-gray-900 mb-2">相关职业</h4>
          <div className="flex flex-wrap gap-1">
            {major.relatedCareers.slice(0, 2).map((career, index) => (
              <span key={index} className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">
                {career}
              </span>
            ))}
            {major.relatedCareers.length > 2 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded">
                +{major.relatedCareers.length - 2}
              </span>
            )}
          </div>
        </div>

        {/* 标签 */}
        <div className="flex flex-wrap gap-2">
          {major.tags.slice(0, 3).map((tag, index) => (
            <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
              {tag}
            </span>
          ))}
        </div>

        {/* 展开的详细信息 */}
        {isExpanded && (
          <div className="pt-4 border-t border-gray-200 space-y-3 animate-slide-up">
            <div>
              <h5 className="text-sm font-semibold text-gray-900 mb-2">录取分数</h5>
              <div className="text-sm text-gray-600">
                最低: {major.admissionScore.min} | 平均: {major.admissionScore.avg} | 最高: {major.admissionScore.max}
              </div>
            </div>
            
            <div>
              <h5 className="text-sm font-semibold text-gray-900 mb-2">开设院校</h5>
              <div className="flex flex-wrap gap-1">
                {major.universities.slice(0, 4).map((university, index) => (
                  <span key={index} className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
                    {university}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-4">
          <Button 
            size="sm" 
            className="flex-1"
            onClick={() => onViewDetails(major.id)}
          >
            查看详情
            <ExternalLink className="w-3 h-3 ml-2" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => onCompare(major.id)}
          >
            <BarChart3 className="w-4 h-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? '收起' : '展开'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
