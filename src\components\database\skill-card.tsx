'use client';

import { useState } from 'react';
import { Zap, Clock, TrendingUp, BookOpen, Award, Users, ChevronRight, Heart, Play, ExternalLink, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface Skill {
  id: string;
  name: string;
  category: 'technical' | 'soft' | 'language' | 'certification';
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  demandLevel: 'high' | 'medium' | 'low';
  relatedCareers: string[];
  learningPath: Array<{
    stage: string;
    duration: string;
    description: string;
  }>;
  learningResources: Array<{
    title: string;
    type: 'course' | 'book' | 'video' | 'article' | 'practice';
    provider: string;
    isFree: boolean;
    rating: number;
  }>;
  prerequisites: string[];
  certifications: string[];
  tags: string[];
}

interface SkillCardProps {
  skill: Skill;
  viewMode: 'grid' | 'list';
  onStartLearning: (id: string) => void;
  onViewPath: (id: string) => void;
}

const LEVEL_CONFIG = {
  beginner: { text: '入门', color: 'text-green-600', bgColor: 'bg-green-100' },
  intermediate: { text: '中级', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  advanced: { text: '高级', color: 'text-red-600', bgColor: 'bg-red-100' },
  expert: { text: '专家', color: 'text-purple-600', bgColor: 'bg-purple-100' }
};

const DEMAND_CONFIG = {
  high: { text: '需求旺盛', color: 'text-green-600', bgColor: 'bg-green-100' },
  medium: { text: '需求一般', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  low: { text: '需求较少', color: 'text-gray-600', bgColor: 'bg-gray-100' }
};

const CATEGORY_ICONS = {
  technical: Zap,
  soft: Users,
  language: BookOpen,
  certification: Award
};

export function SkillCard({ skill, viewMode, onStartLearning, onViewPath }: SkillCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const levelConfig = LEVEL_CONFIG[skill.level];
  const demandConfig = DEMAND_CONFIG[skill.demandLevel];
  const CategoryIcon = CATEGORY_ICONS[skill.category];

  const getTotalDuration = () => {
    // 简单计算总学习时间（这里只是示例）
    return skill.learningPath.length * 2 + '周';
  };

  if (viewMode === 'list') {
    return (
      <Card className="transition-all duration-300 hover:shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <CategoryIcon className="w-5 h-5 text-blue-600" />
                <h3 className="text-xl font-bold text-gray-900">{skill.name}</h3>
                <div className={cn(
                  "px-2 py-1 rounded-full text-xs font-medium",
                  levelConfig.color,
                  levelConfig.bgColor
                )}>
                  {levelConfig.text}
                </div>
                <div className={cn(
                  "px-2 py-1 rounded-full text-xs font-medium",
                  demandConfig.color,
                  demandConfig.bgColor
                )}>
                  {demandConfig.text}
                </div>
              </div>
              
              <p className="text-gray-600 mb-4 line-clamp-2">{skill.description}</p>
              
              <div className="grid grid-cols-4 gap-6 mb-4">
                <div className="flex items-center">
                  <Clock className="w-4 h-4 text-blue-600 mr-2" />
                  <div>
                    <div className="text-sm font-semibold text-gray-900">{getTotalDuration()}</div>
                    <div className="text-xs text-gray-500">学习周期</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Users className="w-4 h-4 text-purple-600 mr-2" />
                  <div>
                    <div className="text-sm font-semibold text-gray-900">{skill.relatedCareers.length}</div>
                    <div className="text-xs text-gray-500">相关职业</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <BookOpen className="w-4 h-4 text-green-600 mr-2" />
                  <div>
                    <div className="text-sm font-semibold text-gray-900">{skill.learningResources.length}</div>
                    <div className="text-xs text-gray-500">学习资源</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Award className="w-4 h-4 text-orange-600 mr-2" />
                  <div>
                    <div className="text-sm font-semibold text-gray-900">{skill.certifications.length}</div>
                    <div className="text-xs text-gray-500">相关认证</div>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {skill.tags.slice(0, 4).map((tag, index) => (
                  <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="flex items-center space-x-2 ml-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsLiked(!isLiked)}
                className={cn(
                  "p-2",
                  isLiked ? "text-red-500" : "text-gray-400"
                )}
              >
                <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
              </Button>
              <Button variant="outline" size="sm" onClick={() => onViewPath(skill.id)}>
                <BookOpen className="w-4 h-4 mr-2" />
                学习路径
              </Button>
              <Button size="sm" onClick={() => onStartLearning(skill.id)}>
                <Play className="w-4 h-4 mr-2" />
                开始学习
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-300 hover:shadow-lg border-0 bg-white/80 backdrop-blur-sm group">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <CategoryIcon className="w-5 h-5 text-blue-600" />
              <CardTitle className="text-lg font-bold text-gray-900 group-hover:text-green-600 transition-colors">
                {skill.name}
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsLiked(!isLiked)}
                className={cn(
                  "p-1",
                  isLiked ? "text-red-500" : "text-gray-400"
                )}
              >
                <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
              </Button>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <div className={cn(
                "inline-flex px-2 py-1 rounded-full text-xs font-medium",
                levelConfig.color,
                levelConfig.bgColor
              )}>
                {levelConfig.text}
              </div>
              <div className={cn(
                "inline-flex px-2 py-1 rounded-full text-xs font-medium",
                demandConfig.color,
                demandConfig.bgColor
              )}>
                {demandConfig.text}
              </div>
            </div>
          </div>
        </div>
        
        <CardDescription className="text-gray-600 line-clamp-3">
          {skill.description}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 关键指标 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center">
            <Clock className="w-4 h-4 text-blue-600 mr-2" />
            <div>
              <div className="text-sm font-semibold text-gray-900">{getTotalDuration()}</div>
              <div className="text-xs text-gray-500">学习周期</div>
            </div>
          </div>
          
          <div className="flex items-center">
            <Users className="w-4 h-4 text-purple-600 mr-2" />
            <div>
              <div className="text-sm font-semibold text-gray-900">{skill.relatedCareers.length}</div>
              <div className="text-xs text-gray-500">相关职业</div>
            </div>
          </div>
        </div>

        {/* 学习路径预览 */}
        <div>
          <h4 className="text-sm font-semibold text-gray-900 mb-2">学习路径</h4>
          <div className="space-y-2">
            {skill.learningPath.slice(0, 2).map((stage, index) => (
              <div key={index} className="flex items-center text-sm">
                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold mr-3">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <span className="text-gray-700">{stage.stage}</span>
                  <span className="text-gray-500 ml-2">({stage.duration})</span>
                </div>
              </div>
            ))}
            {skill.learningPath.length > 2 && (
              <div className="text-xs text-gray-500 ml-9">
                +{skill.learningPath.length - 2} 个阶段
              </div>
            )}
          </div>
        </div>

        {/* 相关职业预览 */}
        <div>
          <h4 className="text-sm font-semibold text-gray-900 mb-2">相关职业</h4>
          <div className="flex flex-wrap gap-1">
            {skill.relatedCareers.slice(0, 2).map((career, index) => (
              <span key={index} className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
                {career}
              </span>
            ))}
            {skill.relatedCareers.length > 2 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded">
                +{skill.relatedCareers.length - 2}
              </span>
            )}
          </div>
        </div>

        {/* 标签 */}
        <div className="flex flex-wrap gap-2">
          {skill.tags.slice(0, 3).map((tag, index) => (
            <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
              {tag}
            </span>
          ))}
        </div>

        {/* 展开的详细信息 */}
        {isExpanded && (
          <div className="pt-4 border-t border-gray-200 space-y-3 animate-slide-up">
            <div>
              <h5 className="text-sm font-semibold text-gray-900 mb-2">前置要求</h5>
              <div className="flex flex-wrap gap-1">
                {skill.prerequisites.map((prereq, index) => (
                  <span key={index} className="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded">
                    {prereq}
                  </span>
                ))}
              </div>
            </div>
            
            <div>
              <h5 className="text-sm font-semibold text-gray-900 mb-2">推荐资源</h5>
              <div className="space-y-2">
                {skill.learningResources.slice(0, 3).map((resource, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <BookOpen className="w-3 h-3 text-gray-400 mr-2" />
                      <span className="text-gray-700">{resource.title}</span>
                      {resource.isFree && (
                        <span className="ml-2 px-1 py-0.5 bg-green-100 text-green-600 text-xs rounded">
                          免费
                        </span>
                      )}
                    </div>
                    <div className="flex items-center">
                      <Star className="w-3 h-3 text-yellow-500 mr-1" />
                      <span className="text-xs text-gray-500">{resource.rating}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h5 className="text-sm font-semibold text-gray-900 mb-2">相关认证</h5>
              <div className="flex flex-wrap gap-1">
                {skill.certifications.map((cert, index) => (
                  <span key={index} className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">
                    {cert}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-4">
          <Button 
            size="sm" 
            className="flex-1"
            onClick={() => onStartLearning(skill.id)}
          >
            <Play className="w-3 h-3 mr-2" />
            开始学习
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => onViewPath(skill.id)}
          >
            <BookOpen className="w-4 h-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? '收起' : '展开'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
