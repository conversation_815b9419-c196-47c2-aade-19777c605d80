'use client';

import { useState } from 'react';
import { Filter, ChevronDown, ChevronUp, X, RotateCcw, Search } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface FilterPanelProps {
  type: 'majors' | 'careers' | 'skills';
  onFiltersChange: (filters: Record<string, unknown>) => void;
}

interface FilterSection {
  id: string;
  title: string;
  type: 'checkbox' | 'range' | 'select';
  options?: { value: string; label: string; count?: number }[];
  range?: { min: number; max: number; step: number; unit?: string };
  isExpanded: boolean;
}

const MAJOR_FILTERS: FilterSection[] = [
  {
    id: 'category',
    title: '专业类别',
    type: 'checkbox',
    isExpanded: true,
    options: [
      { value: 'engineering', label: '工学', count: 156 },
      { value: 'science', label: '理学', count: 89 },
      { value: 'economics', label: '经济学', count: 45 },
      { value: 'management', label: '管理学', count: 67 },
      { value: 'literature', label: '文学', count: 34 },
      { value: 'medicine', label: '医学', count: 78 }
    ]
  },
  {
    id: 'employment-rate',
    title: '就业率',
    type: 'range',
    isExpanded: true,
    range: { min: 70, max: 100, step: 5, unit: '%' }
  },
  {
    id: 'salary',
    title: '起薪范围',
    type: 'range',
    isExpanded: true,
    range: { min: 5000, max: 30000, step: 1000, unit: '元' }
  },
  {
    id: 'job-outlook',
    title: '就业前景',
    type: 'checkbox',
    isExpanded: false,
    options: [
      { value: 'excellent', label: '前景极佳', count: 45 },
      { value: 'good', label: '前景良好', count: 78 },
      { value: 'average', label: '前景一般', count: 34 },
      { value: 'poor', label: '前景较差', count: 12 }
    ]
  },
  {
    id: 'university-type',
    title: '院校类型',
    type: 'checkbox',
    isExpanded: false,
    options: [
      { value: '985', label: '985工程', count: 23 },
      { value: '211', label: '211工程', count: 45 },
      { value: 'double_first_class', label: '双一流', count: 67 },
      { value: 'regular', label: '普通本科', count: 234 }
    ]
  }
];

export function FilterPanel({ type, onFiltersChange }: FilterPanelProps) {
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sections, setSections] = useState<FilterSection[]>(MAJOR_FILTERS);

  const toggleSection = (sectionId: string) => {
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { ...section, isExpanded: !section.isExpanded }
          : section
      )
    );
  };

  const updateFilter = (sectionId: string, value: unknown) => {
    const newFilters = { ...filters, [sectionId]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    setFilters({});
    onFiltersChange({});
  };

  const getActiveFilterCount = () => {
    return Object.keys(filters).filter(key => {
      const value = filters[key];
      if (Array.isArray(value)) return value.length > 0;
      if (typeof value === 'object' && value !== null) return Object.keys(value).length > 0;
      return value !== undefined && value !== null && value !== '';
    }).length;
  };

  const renderCheckboxFilter = (section: FilterSection) => (
    <div className="space-y-2">
      {section.options?.map((option) => (
        <label key={option.value} className="flex items-center space-x-3 cursor-pointer group">
          <input
            type="checkbox"
            checked={filters[section.id]?.includes(option.value) || false}
            onChange={(e) => {
              const currentValues = filters[section.id] || [];
              const newValues = e.target.checked
                ? [...currentValues, option.value]
                : currentValues.filter((v: string) => v !== option.value);
              updateFilter(section.id, newValues);
            }}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700 group-hover:text-gray-900 flex-1">
            {option.label}
          </span>
          {option.count && (
            <span className="text-xs text-gray-400">
              {option.count}
            </span>
          )}
        </label>
      ))}
    </div>
  );

  const renderRangeFilter = (section: FilterSection) => {
    if (!section.range) return null;
    
    const { min, max, step, unit } = section.range;
    const currentMin = filters[section.id]?.min || min;
    const currentMax = filters[section.id]?.max || max;

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>{currentMin}{unit}</span>
          <span>{currentMax}{unit}</span>
        </div>
        <div className="space-y-3">
          <div>
            <label className="text-xs text-gray-500">最小值</label>
            <input
              type="range"
              min={min}
              max={max}
              step={step}
              value={currentMin}
              onChange={(e) => {
                updateFilter(section.id, {
                  ...filters[section.id],
                  min: parseInt(e.target.value)
                });
              }}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
          <div>
            <label className="text-xs text-gray-500">最大值</label>
            <input
              type="range"
              min={min}
              max={max}
              step={step}
              value={currentMax}
              onChange={(e) => {
                updateFilter(section.id, {
                  ...filters[section.id],
                  max: parseInt(e.target.value)
                });
              }}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className="sticky top-24 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
            <Filter className="w-5 h-5 mr-2" />
            筛选条件
            {getActiveFilterCount() > 0 && (
              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                {getActiveFilterCount()}
              </span>
            )}
          </CardTitle>
          {getActiveFilterCount() > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-gray-500 hover:text-gray-700"
            >
              <RotateCcw className="w-4 h-4 mr-1" />
              清除
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {sections.map((section) => (
          <div key={section.id} className="border-b border-gray-100 last:border-b-0 pb-4 last:pb-0">
            <button
              onClick={() => toggleSection(section.id)}
              className="flex items-center justify-between w-full text-left mb-3 hover:text-blue-600 transition-colors"
            >
              <span className="font-medium text-gray-900">{section.title}</span>
              {section.isExpanded ? (
                <ChevronUp className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              )}
            </button>

            {section.isExpanded && (
              <div className="animate-slide-up">
                {section.type === 'checkbox' && renderCheckboxFilter(section)}
                {section.type === 'range' && renderRangeFilter(section)}
              </div>
            )}
          </div>
        ))}
      </CardContent>

      {/* 应用筛选按钮 */}
      <div className="p-4 border-t border-gray-100">
        <Button 
          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          onClick={() => onFiltersChange(filters)}
        >
          应用筛选条件
        </Button>
      </div>

      {/* 自定义CSS样式 */}
      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 16px;
          width: 16px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .slider::-moz-range-thumb {
          height: 16px;
          width: 16px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </Card>
  );
}
