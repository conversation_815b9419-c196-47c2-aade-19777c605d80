'use client';

import { useState } from 'react';
import { Brain, BookOpen, TrendingUp, ChevronRight, Clock, Users, Target, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

const FEATURES = [
  {
    id: 'assessment',
    title: 'AI职业测评',
    description: '基于心理学和大数据的智能测评系统',
    icon: Brain,
    color: 'from-primary to-cyan-500',
    step: '第一步',
    details: [
      { icon: Clock, text: '15-30分钟完成' },
      { icon: Target, text: '多维度分析' },
      { icon: Zap, text: 'AI智能推荐' }
    ],
    features: [
      '性格特质分析',
      '兴趣爱好匹配',
      '能力潜力评估',
      '职业倾向预测'
    ],
    cta: '开始测评',
    href: '/assessment'
  },
  {
    id: 'explore',
    title: '专业-职业探索',
    description: '深度了解专业内容和职业发展路径',
    icon: BookOpen,
    color: 'from-purple-500 to-pink-500',
    step: '第二步',
    details: [
      { icon: BookOpen, text: '1000+专业详解' },
      { icon: Users, text: '5000+职业数据' },
      { icon: TrendingUp, text: '实时就业趋势' }
    ],
    features: [
      '专业课程设置',
      '就业前景分析',
      '薪资水平统计',
      '技能要求清单'
    ],
    cta: '探索专业',
    href: '/majors'
  },
  {
    id: 'plan',
    title: '生成志愿方案',
    description: '个性化志愿填报建议和录取概率分析',
    icon: TrendingUp,
    color: 'from-success to-emerald-500',
    step: '第三步',
    details: [
      { icon: Target, text: '精准匹配' },
      { icon: TrendingUp, text: '录取概率' },
      { icon: Users, text: '专家建议' }
    ],
    features: [
      '智能志愿推荐',
      '录取概率计算',
      '风险评估分析',
      '备选方案生成'
    ],
    cta: '生成方案',
    href: '/volunteer'
  }
];

export function FeatureCards() {
  const [activeCard, setActiveCard] = useState<string | null>(null);

  return (
    <div className="grid md:grid-cols-3 gap-8">
      {FEATURES.map((feature, index) => {
        const Icon = feature.icon;
        const isActive = activeCard === feature.id;
        
        return (
          <Card
            key={feature.id}
            className={cn(
              "relative overflow-hidden transition-all duration-300 cursor-pointer group hover-lift glass",
              isActive ? "scale-105 shadow-2xl border-primary/50" : "hover:scale-[1.02] hover:shadow-xl"
            )}
            onMouseEnter={() => setActiveCard(feature.id)}
            onMouseLeave={() => setActiveCard(null)}
          >
            {/* 步骤标识 */}
            <div className="absolute top-4 right-4 bg-card/80 backdrop-blur-sm rounded-full px-3 py-1 text-xs font-semibold text-muted-foreground border border-border">
              {feature.step}
            </div>

            {/* 背景装饰 */}
            <div className={cn(
              "absolute -top-10 -right-10 w-20 h-20 rounded-full opacity-10 transition-opacity duration-300",
              `bg-gradient-to-br ${feature.color}`,
              isActive && "opacity-20"
            )} />

            <CardHeader className="pb-4">
              <div className={cn(
                "w-12 h-12 rounded-xl flex items-center justify-center mb-4 transition-transform duration-300 hover-scale",
                `bg-gradient-to-br ${feature.color}`,
                "shadow-lg"
              )}>
                <Icon className="w-6 h-6 text-white" />
              </div>

              <CardTitle className="text-xl font-bold text-foreground">
                {feature.title}
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                {feature.description}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* 特点列表 */}
              <div className="space-y-2">
                {feature.details.map((detail, idx) => {
                  const DetailIcon = detail.icon;
                  return (
                    <div key={idx} className="flex items-center text-sm text-muted-foreground animate-fade-in" style={{ animationDelay: `${idx * 0.1}s` }}>
                      <DetailIcon className="w-4 h-4 mr-2 text-primary/70" />
                      {detail.text}
                    </div>
                  );
                })}
              </div>

              {/* 展开的功能列表 */}
              <div className={cn(
                "transition-all duration-500 overflow-hidden",
                isActive ? "max-h-40 opacity-100" : "max-h-0 opacity-0"
              )}>
                <div className="pt-4 border-t border-border">
                  <div className="grid grid-cols-2 gap-2">
                    {feature.features.map((feat, idx) => (
                      <div key={idx} className="text-xs text-muted-foreground flex items-center animate-slide-up" style={{ animationDelay: `${idx * 0.05}s` }}>
                        <div className="w-1.5 h-1.5 bg-primary/50 rounded-full mr-2" />
                        {feat}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* CTA按钮 */}
              <Button
                className={cn(
                  "w-full group-hover:shadow-lg transition-all duration-300 hover-scale",
                  `bg-gradient-to-r ${feature.color} hover:opacity-90`
                )}
                onClick={() => console.log(`Navigate to ${feature.href}`)}
              >
                {feature.cta}
                <ChevronRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
              </Button>
            </CardContent>

            {/* 连接线（除了最后一个卡片） */}
            {index < FEATURES.length - 1 && (
              <div className="hidden md:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-border to-transparent transform -translate-y-1/2 z-10">
                <div className="absolute right-0 top-1/2 w-2 h-2 bg-primary/50 rounded-full transform translate-x-1 -translate-y-1/2 animate-pulse" />
              </div>
            )}
          </Card>
        );
      })}
    </div>
  );
}
