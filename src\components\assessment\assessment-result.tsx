'use client';

import { useState } from 'react';
import { Download, Share2, BookO<PERSON>, TrendingUp, Users, Award, ChevronRight, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PersonalityChart } from '@/components/assessment/personality-chart';
import { CareerRecommendations } from '@/components/assessment/career-recommendations';
import { GrowthPath } from '@/components/assessment/growth-path';

interface AssessmentResultProps {
  assessmentId: string;
  onRestart: () => void;
}

// 模拟测评结果数据
const MOCK_RESULT = {
  personalityType: 'ENFP',
  personalityName: '活动家',
  personalityDescription: '热情、有创造力和社交能力强的自由精神，总能找到理由微笑。',
  traits: [
    { name: '外向性', score: 85, description: '喜欢与人交往，从社交中获得能量' },
    { name: '直觉性', score: 78, description: '关注可能性和未来，善于创新思考' },
    { name: '情感性', score: 82, description: '重视和谐，以价值观为决策依据' },
    { name: '感知性', score: 75, description: '灵活适应，喜欢保持选择的开放性' }
  ],
  strengths: [
    '富有创造力和想象力',
    '优秀的沟通和人际交往能力',
    '热情洋溢，能够激励他人',
    '适应性强，能够应对变化'
  ],
  challenges: [
    '可能缺乏持续专注力',
    '有时过于理想化',
    '可能忽视细节和实际问题',
    '容易受到批评的影响'
  ],
  careerMatches: [
    {
      career: '产品经理',
      matchScore: 92,
      reasons: ['需要创新思维', '重视用户体验', '需要跨部门协调'],
      salaryRange: '20K-40K',
      outlook: 'excellent'
    },
    {
      career: '市场营销专员',
      matchScore: 88,
      reasons: ['需要创意策划', '重视品牌传播', '需要市场洞察'],
      salaryRange: '15K-30K',
      outlook: 'good'
    },
    {
      career: 'UI/UX设计师',
      matchScore: 85,
      reasons: ['需要创意设计', '重视用户体验', '需要美学感知'],
      salaryRange: '18K-35K',
      outlook: 'excellent'
    }
  ]
};

export function AssessmentResult({ assessmentId, onRestart }: AssessmentResultProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'careers' | 'growth'>('overview');

  const tabs = [
    { id: 'overview', name: '性格概览', icon: Users },
    { id: 'careers', name: '职业推荐', icon: Briefcase },
    { id: 'growth', name: '成长路径', icon: TrendingUp }
  ];

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 结果头部 */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-4">
          <Award className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          测评完成！
        </h1>
        <p className="text-lg text-gray-600 mb-6">
          恭喜你完成了职业测评，以下是你的个性化分析报告
        </p>
        
        {/* 操作按钮 */}
        <div className="flex justify-center space-x-4">
          <Button variant="outline" className="flex items-center">
            <Download className="w-4 h-4 mr-2" />
            下载报告
          </Button>
          <Button variant="outline" className="flex items-center">
            <Share2 className="w-4 h-4 mr-2" />
            分享结果
          </Button>
          <Button onClick={onRestart} className="flex items-center">
            重新测评
          </Button>
        </div>
      </div>

      {/* 性格类型卡片 */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0 shadow-lg">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl font-bold text-white">{MOCK_RESULT.personalityType}</span>
          </div>
          <CardTitle className="text-2xl text-gray-900">
            {MOCK_RESULT.personalityName}
          </CardTitle>
          <CardDescription className="text-lg text-gray-700">
            {MOCK_RESULT.personalityDescription}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 标签页导航 */}
      <div className="flex justify-center">
        <div className="flex bg-gray-100 rounded-lg p-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as 'careers' | 'majors' | 'skills')}
                className={`
                  flex items-center px-6 py-3 rounded-md text-sm font-medium transition-all duration-200
                  ${activeTab === tab.id
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                  }
                `}
              >
                <Icon className="w-4 h-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </div>
      </div>

      {/* 标签页内容 */}
      <div className="animate-fade-in">
        {activeTab === 'overview' && (
          <div className="grid lg:grid-cols-2 gap-8">
            {/* 性格特质雷达图 */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
                  性格特质分析
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PersonalityChart traits={MOCK_RESULT.traits} />
              </CardContent>
            </Card>

            {/* 优势与挑战 */}
            <div className="space-y-6">
              <Card className="bg-green-50 border-green-200">
                <CardHeader>
                  <CardTitle className="text-green-800 flex items-center">
                    <Star className="w-5 h-5 mr-2" />
                    你的优势
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {MOCK_RESULT.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start text-green-700">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                        {strength}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-orange-50 border-orange-200">
                <CardHeader>
                  <CardTitle className="text-orange-800 flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2" />
                    发展建议
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {MOCK_RESULT.challenges.map((challenge, index) => (
                      <li key={index} className="flex items-start text-orange-700">
                        <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                        {challenge}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {activeTab === 'careers' && (
          <CareerRecommendations careers={MOCK_RESULT.careerMatches} />
        )}

        {activeTab === 'growth' && (
          <GrowthPath personalityType={MOCK_RESULT.personalityType} />
        )}
      </div>

      {/* 下一步行动 */}
      <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0">
        <CardContent className="p-8 text-center">
          <h3 className="text-2xl font-bold mb-4">准备好探索你的职业道路了吗？</h3>
          <p className="text-lg mb-6 opacity-90">
            基于你的测评结果，我们为你准备了更多个性化的职业探索工具
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="text-blue-600">
              <BookOpen className="w-5 h-5 mr-2" />
              探索推荐专业
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              <Users className="w-5 h-5 mr-2" />
              查看就业数据
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
