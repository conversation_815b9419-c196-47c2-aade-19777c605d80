'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, User, Send, Heart, BookOpen, MapPin, Sparkles, RefreshCw, Bookmark, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CareerProfileCard } from '@/components/assessment/career-profile-card';
import { cn } from '@/lib/utils';

interface AIConversationAssessmentProps {
  sessionId: string;
  onShowResults?: () => void;
  showResults?: boolean;
}

interface Message {
  id: string;
  type: 'bot' | 'user';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

interface UserProfile {
  interests: string[];
  subjects: string[];
  cities: string[];
  workStyle: string[];
  values: string[];
}

interface CareerProfile {
  id: string;
  title: string;
  description: string;
  matchScore: number;
  matchReasons: string[];
  salaryRange: string;
  growthPath: string[];
  requiredSkills: string[];
  workEnvironment: string;
  isBookmarked: boolean;
}

// 模拟AI对话流程
const CONVERSATION_FLOW = [
  {
    id: 'greeting',
    message: '你好！我是你的AI职业顾问小助手 🤖 很高兴认识你！让我们通过轻松的对话来了解你的职业兴趣吧。首先，能告诉我你平时最喜欢做什么吗？比如看书、运动、编程、画画等等～',
    suggestions: ['阅读学习', '运动健身', '编程开发', '设计创作', '音乐艺术', '社交聊天']
  },
  {
    id: 'subjects',
    message: '很棒！看起来你是个很有趣的人 😊 那么在学习方面，你最擅长或者最感兴趣的科目是什么呢？',
    suggestions: ['数学', '物理', '化学', '生物', '语文', '英语', '历史', '地理', '政治', '计算机']
  },
  {
    id: 'location',
    message: '了解了！你的学习能力很不错呢 👍 未来工作的话，你比较倾向于在哪些城市发展？或者有什么特别的地理偏好吗？',
    suggestions: ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '不限地区']
  },
  {
    id: 'workstyle',
    message: '好的！关于工作方式，你更喜欢哪种类型呢？',
    suggestions: ['独立工作', '团队协作', '领导他人', '创新研究', '稳定执行', '灵活自由']
  }
];

// 模拟职业画像数据
const MOCK_CAREER_PROFILES: CareerProfile[] = [
  {
    id: 'software-engineer',
    title: '软件工程师',
    description: '设计和开发软件应用程序，解决技术问题，创造数字化解决方案',
    matchScore: 92,
    matchReasons: [
      '你对编程开发很感兴趣 → 核心技能匹配',
      '数学和计算机基础扎实 → 专业基础符合',
      '喜欢独立工作和创新 → 工作方式契合'
    ],
    salaryRange: '15K-40K',
    growthPath: ['初级工程师', '中级工程师', '高级工程师', '技术专家/架构师'],
    requiredSkills: ['编程语言', '算法设计', '系统架构', '问题解决'],
    workEnvironment: '科技公司、互联网企业、软件开发团队',
    isBookmarked: false
  },
  {
    id: 'product-manager',
    title: '产品经理',
    description: '负责产品规划、设计和管理，协调团队资源，推动产品成功',
    matchScore: 85,
    matchReasons: [
      '你喜欢团队协作 → 沟通协调能力强',
      '对创新研究感兴趣 → 产品思维活跃',
      '综合能力较强 → 适合跨领域工作'
    ],
    salaryRange: '20K-50K',
    growthPath: ['产品助理', '产品经理', '高级产品经理', '产品总监'],
    requiredSkills: ['产品规划', '用户研究', '数据分析', '项目管理'],
    workEnvironment: '互联网公司、科技企业、创业公司',
    isBookmarked: false
  },
  {
    id: 'data-scientist',
    title: '数据科学家',
    description: '运用统计学和机器学习方法分析数据，为业务决策提供洞察',
    matchScore: 88,
    matchReasons: [
      '数学基础扎实 → 统计分析能力强',
      '对计算机技术感兴趣 → 编程技能匹配',
      '喜欢独立研究 → 适合深度分析工作'
    ],
    salaryRange: '25K-60K',
    growthPath: ['数据分析师', '数据科学家', '高级数据科学家', '首席数据官'],
    requiredSkills: ['统计学', '机器学习', 'Python/R', '业务理解'],
    workEnvironment: '科技公司、金融机构、咨询公司',
    isBookmarked: false
  }
];

export function AIConversationAssessment({ sessionId, onShowResults, showResults = false }: AIConversationAssessmentProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [userProfile, setUserProfile] = useState<UserProfile>({
    interests: [],
    subjects: [],
    cities: [],
    workStyle: [],
    values: []
  });
  const [careerProfiles, setCareerProfiles] = useState<CareerProfile[]>(MOCK_CAREER_PROFILES);
  const [showProfiles, setShowProfiles] = useState(showResults);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (!showResults) {
      // 开始对话
      setTimeout(() => {
        addBotMessage(CONVERSATION_FLOW[0].message, CONVERSATION_FLOW[0].suggestions);
      }, 1000);
    } else {
      setShowProfiles(true);
    }
  }, [showResults]);

  const addBotMessage = (content: string, suggestions?: string[]) => {
    setIsTyping(true);
    setTimeout(() => {
      const message: Message = {
        id: `bot_${Date.now()}`,
        type: 'bot',
        content,
        timestamp: new Date(),
        suggestions
      };
      setMessages(prev => [...prev, message]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const addUserMessage = (content: string) => {
    const message: Message = {
      id: `user_${Date.now()}`,
      type: 'user',
      content,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
  };

  const handleSendMessage = (message?: string) => {
    const content = message || currentInput.trim();
    if (!content) return;

    addUserMessage(content);
    setCurrentInput('');

    // 更新用户画像
    updateUserProfile(content);

    // 继续对话流程
    setTimeout(() => {
      proceedConversation();
    }, 1500);
  };

  const updateUserProfile = (input: string) => {
    const step = CONVERSATION_FLOW[currentStep];
    const newProfile = { ...userProfile };

    switch (step.id) {
      case 'greeting':
        newProfile.interests.push(input);
        break;
      case 'subjects':
        newProfile.subjects.push(input);
        break;
      case 'location':
        newProfile.cities.push(input);
        break;
      case 'workstyle':
        newProfile.workStyle.push(input);
        break;
    }

    setUserProfile(newProfile);
  };

  const proceedConversation = () => {
    const nextStep = currentStep + 1;
    
    if (nextStep < CONVERSATION_FLOW.length) {
      setCurrentStep(nextStep);
      const nextFlow = CONVERSATION_FLOW[nextStep];
      addBotMessage(nextFlow.message, nextFlow.suggestions);
    } else {
      // 对话结束，生成职业画像
      generateCareerProfiles();
    }
  };

  const generateCareerProfiles = () => {
    addBotMessage(
      '太棒了！基于我们的对话，我已经为你分析出了几个非常匹配的职业方向 ✨ 让我来为你展示这些职业画像，你可以仔细对比一下～'
    );
    
    setTimeout(() => {
      setShowProfiles(true);
      onShowResults?.();
    }, 2000);
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleRefreshProfiles = () => {
    // 重新生成职业画像
    addBotMessage('让我为你重新分析一下，稍等片刻... 🔄');
    setTimeout(() => {
      addBotMessage('好的！我为你找到了一些新的职业方向，看看这些怎么样？');
    }, 2000);
  };

  const handleBookmarkProfile = (profileId: string) => {
    setCareerProfiles(prev => 
      prev.map(profile => 
        profile.id === profileId 
          ? { ...profile, isBookmarked: !profile.isBookmarked }
          : profile
      )
    );
  };

  if (showProfiles) {
    return (
      <div className="max-w-6xl mx-auto space-y-8">
        {/* 对话历史摘要 */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0">
          <CardContent className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
              <Sparkles className="w-5 h-5 mr-2 text-blue-600" />
              基于你的对话，我了解到...
            </h3>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>兴趣爱好：</strong> {userProfile.interests.join('、') || '待补充'}
              </div>
              <div>
                <strong>擅长科目：</strong> {userProfile.subjects.join('、') || '待补充'}
              </div>
              <div>
                <strong>理想城市：</strong> {userProfile.cities.join('、') || '待补充'}
              </div>
              <div>
                <strong>工作偏好：</strong> {userProfile.workStyle.join('、') || '待补充'}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 职业画像推荐 */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">为你推荐的职业画像</h2>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleRefreshProfiles}>
                <RefreshCw className="w-4 h-4 mr-2" />
                换一批
              </Button>
              <Button variant="outline" size="sm">
                <BarChart3 className="w-4 h-4 mr-2" />
                对比分析
              </Button>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {careerProfiles.map((profile) => (
              <CareerProfileCard
                key={profile.id}
                profile={profile}
                onBookmark={() => handleBookmarkProfile(profile.id)}
              />
            ))}
          </div>
        </div>

        {/* 继续对话 */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardContent className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4 text-center">
              还有其他想了解的吗？
            </h3>
            <div className="flex space-x-2">
              <Input
                ref={inputRef}
                value={currentInput}
                onChange={(e) => setCurrentInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="继续和AI对话，比如：我想了解更多关于薪资的信息..."
                className="flex-1"
              />
              <Button onClick={() => handleSendMessage()}>
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* 对话区域 */}
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="space-y-4 max-h-[500px] overflow-y-auto">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex items-start space-x-3",
                  message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                )}
              >
                {/* 头像 */}
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
                  message.type === 'bot' 
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600' 
                    : 'bg-gradient-to-r from-green-500 to-emerald-500'
                )}>
                  {message.type === 'bot' ? (
                    <Bot className="w-4 h-4 text-white" />
                  ) : (
                    <User className="w-4 h-4 text-white" />
                  )}
                </div>

                {/* 消息内容 */}
                <div className={cn(
                  "flex-1 max-w-md",
                  message.type === 'user' ? 'text-right' : ''
                )}>
                  <div className={cn(
                    "rounded-2xl px-4 py-3 text-sm",
                    message.type === 'bot'
                      ? 'bg-gray-100 text-gray-900'
                      : 'bg-blue-600 text-white'
                  )}>
                    {message.content}
                  </div>

                  {/* 建议选项 */}
                  {message.suggestions && message.type === 'bot' && (
                    <div className="mt-3 flex flex-wrap gap-2">
                      {message.suggestions.map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => handleSuggestionClick(suggestion)}
                          className="text-xs hover:bg-blue-50 hover:border-blue-300"
                        >
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  )}

                  <div className="text-xs text-gray-500 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}

            {/* 打字指示器 */}
            {isTyping && (
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div className="bg-gray-100 rounded-2xl px-4 py-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* 输入区域 */}
          <div className="mt-6 flex space-x-2">
            <Input
              ref={inputRef}
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入你的回答..."
              className="flex-1"
            />
            <Button onClick={() => handleSendMessage()}>
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
