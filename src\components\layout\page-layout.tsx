'use client';

import { ReactNode } from 'react';
import { Navbar } from './navbar';
import { Footer } from './footer';
import { Breadcrumb } from './breadcrumb';
import { AnnouncementBar, AnnouncementCarousel } from './announcement-bar';
import { cn } from '@/lib/utils';

interface PageLayoutProps {
  children: ReactNode;
  className?: string;
  navbarVariant?: 'default' | 'transparent';
  showNavbar?: boolean;
  showFooter?: boolean;
  showBreadcrumb?: boolean;
  showAnnouncement?: boolean;
  currentPath?: string;
  breadcrumbItems?: Array<{ label: string; href?: string; current?: boolean }>;
  announcement?: {
    message: string;
    type?: 'info' | 'success' | 'warning' | 'promotion';
    actionText?: string;
    actionHref?: string;
    onAction?: () => void;
  };
  backgroundPattern?: 'gradient' | 'dots' | 'grid' | 'none';
}

export function PageLayout({
  children,
  className,
  navbarVariant = 'default',
  showNavbar = true,
  showFooter = true,
  showBreadcrumb = false,
  showAnnouncement = false,
  currentPath,
  breadcrumbItems,
  announcement,
  backgroundPattern = 'gradient'
}: PageLayoutProps) {
  const getBackgroundClass = () => {
    switch (backgroundPattern) {
      case 'gradient':
        return 'bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900';
      case 'dots':
        return 'bg-background bg-dot-pattern';
      case 'grid':
        return 'bg-background bg-grid-pattern';
      case 'none':
        return 'bg-background';
      default:
        return 'bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900';
    }
  };

  return (
    <div className={cn("min-h-screen flex flex-col", getBackgroundClass(), className)}>
      {/* 公告栏 */}
      {showAnnouncement && announcement && (
        <AnnouncementBar {...announcement} />
      )}

      {/* 导航栏 */}
      {showNavbar && (
        <Navbar
          variant={navbarVariant}
          currentPath={currentPath}
          showSearch={true}
        />
      )}

      {/* 面包屑导航 */}
      {showBreadcrumb && breadcrumbItems && (
        <div className="border-b border-border bg-card/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <Breadcrumb items={breadcrumbItems} />
          </div>
        </div>
      )}

      <main className="relative flex-1">
        {children}
      </main>

      {showFooter && <Footer />}
    </div>
  );
}

// 页面容器组件
interface PageContainerProps {
  children: ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '7xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export function PageContainer({ 
  children, 
  className,
  maxWidth = '7xl',
  padding = 'md'
}: PageContainerProps) {
  const getMaxWidthClass = () => {
    switch (maxWidth) {
      case 'sm': return 'max-w-sm';
      case 'md': return 'max-w-md';
      case 'lg': return 'max-w-lg';
      case 'xl': return 'max-w-xl';
      case '2xl': return 'max-w-2xl';
      case '7xl': return 'max-w-7xl';
      case 'full': return 'max-w-full';
      default: return 'max-w-7xl';
    }
  };

  const getPaddingClass = () => {
    switch (padding) {
      case 'none': return '';
      case 'sm': return 'px-4 py-4';
      case 'md': return 'px-4 sm:px-6 lg:px-8 py-8';
      case 'lg': return 'px-4 sm:px-6 lg:px-8 py-12';
      default: return 'px-4 sm:px-6 lg:px-8 py-8';
    }
  };

  return (
    <div className={cn(
      "mx-auto",
      getMaxWidthClass(),
      getPaddingClass(),
      className
    )}>
      {children}
    </div>
  );
}

// 页面标题组件
interface PageHeaderProps {
  title: string;
  description?: string;
  children?: ReactNode;
  className?: string;
  centered?: boolean;
}

export function PageHeader({ 
  title, 
  description, 
  children, 
  className,
  centered = false 
}: PageHeaderProps) {
  return (
    <div className={cn(
      "mb-8",
      centered && "text-center",
      className
    )}>
      <h1 className={cn(
        "text-3xl md:text-4xl font-bold text-foreground mb-4",
        "animate-fade-in"
      )}>
        {title}
      </h1>
      {description && (
        <p className={cn(
          "text-lg text-muted-foreground mb-6",
          "animate-slide-up",
          centered ? "max-w-2xl mx-auto" : "max-w-3xl"
        )}>
          {description}
        </p>
      )}
      {children && (
        <div className="animate-slide-up">
          {children}
        </div>
      )}
    </div>
  );
}

// 页面部分组件
interface PageSectionProps {
  children: ReactNode;
  className?: string;
  id?: string;
  background?: 'transparent' | 'muted' | 'card';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export function PageSection({ 
  children, 
  className,
  id,
  background = 'transparent',
  padding = 'lg'
}: PageSectionProps) {
  const getBackgroundClass = () => {
    switch (background) {
      case 'muted': return 'bg-muted/50';
      case 'card': return 'bg-card border border-border rounded-lg shadow-sm';
      default: return '';
    }
  };

  const getPaddingClass = () => {
    switch (padding) {
      case 'none': return '';
      case 'sm': return 'py-8';
      case 'md': return 'py-12';
      case 'lg': return 'py-16';
      default: return 'py-16';
    }
  };

  return (
    <section 
      id={id}
      className={cn(
        getPaddingClass(),
        getBackgroundClass(),
        className
      )}
    >
      {children}
    </section>
  );
}
