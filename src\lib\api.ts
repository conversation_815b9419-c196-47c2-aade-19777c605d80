import { ApiResponse, PaginationParams, SortParams, SearchFilters } from '@/types';
import { API_CONFIG } from './constants';

// API客户端类
class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor(baseURL: string = API_CONFIG.BASE_URL, timeout: number = API_CONFIG.TIMEOUT) {
    this.baseURL = baseURL;
    this.timeout = timeout;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时');
        }
        throw error;
      }
      
      throw new Error('网络请求失败');
    }
  }

  async get<T>(endpoint: string, params?: Record<string, unknown>): Promise<ApiResponse<T>> {
    const url = new URL(endpoint, this.baseURL);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }

    return this.request<T>(url.pathname + url.search);
  }

  async post<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }
}

// 创建API客户端实例
export const apiClient = new ApiClient();

// 用户相关API
export const userApi = {
  getProfile: () => apiClient.get('/user/profile'),
  updateProfile: (data: Record<string, unknown>) => apiClient.put('/user/profile', data),
  getAssessments: (params?: PaginationParams) => 
    apiClient.get('/user/assessments', params),
  getFavorites: (params?: PaginationParams) => 
    apiClient.get('/user/favorites', params),
  addFavorite: (type: string, id: string) => 
    apiClient.post('/user/favorites', { type, id }),
  removeFavorite: (type: string, id: string) => 
    apiClient.delete(`/user/favorites/${type}/${id}`),
};

// 测评相关API
export const assessmentApi = {
  getTypes: () => apiClient.get('/assessments/types'),
  start: (type: string) => apiClient.post('/assessments/start', { type }),
  getQuestions: (assessmentId: string) => 
    apiClient.get(`/assessments/${assessmentId}/questions`),
  submitAnswers: (assessmentId: string, answers: Record<string, unknown>[]) =>
    apiClient.post(`/assessments/${assessmentId}/answers`, { answers }),
  getResult: (assessmentId: string) => 
    apiClient.get(`/assessments/${assessmentId}/result`),
  getHistory: (params?: PaginationParams) => 
    apiClient.get('/assessments/history', params),
};

// 职业相关API
export const careerApi = {
  search: (query: string, filters?: SearchFilters, pagination?: PaginationParams, sort?: SortParams) => 
    apiClient.get('/careers/search', { query, ...filters, ...pagination, ...sort }),
  getById: (id: string) => apiClient.get(`/careers/${id}`),
  getCategories: () => apiClient.get('/careers/categories'),
  getRecommended: (userId?: string) => apiClient.get('/careers/recommended', { userId }),
  getTrending: () => apiClient.get('/careers/trending'),
  getRelated: (careerId: string) => apiClient.get(`/careers/${careerId}/related`),
};

// 专业相关API
export const majorApi = {
  search: (query: string, filters?: SearchFilters, pagination?: PaginationParams, sort?: SortParams) => 
    apiClient.get('/majors/search', { query, ...filters, ...pagination, ...sort }),
  getById: (id: string) => apiClient.get(`/majors/${id}`),
  getCategories: () => apiClient.get('/majors/categories'),
  getByCareer: (careerId: string) => apiClient.get(`/majors/by-career/${careerId}`),
  getAdmissionData: (majorId: string, year?: number, province?: string) => 
    apiClient.get(`/majors/${majorId}/admission`, { year, province }),
};

// 技能相关API
export const skillApi = {
  search: (query: string, filters?: SearchFilters, pagination?: PaginationParams) => 
    apiClient.get('/skills/search', { query, ...filters, ...pagination }),
  getById: (id: string) => apiClient.get(`/skills/${id}`),
  getCategories: () => apiClient.get('/skills/categories'),
  getByCareer: (careerId: string) => apiClient.get(`/skills/by-career/${careerId}`),
  getLearningResources: (skillId: string) => 
    apiClient.get(`/skills/${skillId}/resources`),
};

// 大学相关API
export const universityApi = {
  search: (query: string, filters?: SearchFilters, pagination?: PaginationParams, sort?: SortParams) => 
    apiClient.get('/universities/search', { query, ...filters, ...pagination, ...sort }),
  getById: (id: string) => apiClient.get(`/universities/${id}`),
  getByMajor: (majorId: string) => apiClient.get(`/universities/by-major/${majorId}`),
  getRankings: (type?: string) => apiClient.get('/universities/rankings', { type }),
  getAdmissionData: (universityId: string, year?: number, province?: string) => 
    apiClient.get(`/universities/${universityId}/admission`, { year, province }),
};

// 就业数据API
export const employmentApi = {
  getByMajor: (majorId: string, year?: number, region?: string) => 
    apiClient.get(`/employment/by-major/${majorId}`, { year, region }),
  getByCareer: (careerId: string, year?: number, region?: string) => 
    apiClient.get(`/employment/by-career/${careerId}`, { year, region }),
  getSalaryTrends: (type: 'major' | 'career', id: string, years?: number) => 
    apiClient.get(`/employment/salary-trends/${type}/${id}`, { years }),
  getIndustryData: (industry: string, year?: number) => 
    apiClient.get(`/employment/industry/${industry}`, { year }),
  getRegionalData: (region: string, year?: number) => 
    apiClient.get(`/employment/regional/${region}`, { year }),
};

// 志愿方案API
export const volunteerApi = {
  getPlans: (userId: string) => apiClient.get(`/volunteer/plans/${userId}`),
  createPlan: (data: Record<string, unknown>) => apiClient.post('/volunteer/plans', data),
  updatePlan: (planId: string, data: Record<string, unknown>) =>
    apiClient.put(`/volunteer/plans/${planId}`, data),
  deletePlan: (planId: string) => apiClient.delete(`/volunteer/plans/${planId}`),
  generateRecommendations: (preferences: Record<string, unknown>) =>
    apiClient.post('/volunteer/recommendations', preferences),
  calculateProbability: (score: number, majorId: string, universityId: string, province: string) => 
    apiClient.get('/volunteer/probability', { score, majorId, universityId, province }),
};

// 搜索API
export const searchApi = {
  global: (query: string, type?: string) => 
    apiClient.get('/search/global', { query, type }),
  suggestions: (query: string, type?: string) => 
    apiClient.get('/search/suggestions', { query, type }),
  trending: () => apiClient.get('/search/trending'),
  history: (userId: string) => apiClient.get(`/search/history/${userId}`),
  saveHistory: (userId: string, query: string, type: string) => 
    apiClient.post('/search/history', { userId, query, type }),
};

// 内容API
export const contentApi = {
  getNews: (category?: string, limit?: number) => 
    apiClient.get('/content/news', { category, limit }),
  getArticles: (category?: string, pagination?: PaginationParams) => 
    apiClient.get('/content/articles', { category, ...pagination }),
  getArticleById: (id: string) => apiClient.get(`/content/articles/${id}`),
  getStories: (type?: string, pagination?: PaginationParams) => 
    apiClient.get('/content/stories', { type, ...pagination }),
  getStoryById: (id: string) => apiClient.get(`/content/stories/${id}`),
};

// 错误处理工具
export function handleApiError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return '网络请求失败，请稍后重试';
}

// 重试工具
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = API_CONFIG.RETRY_ATTEMPTS
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      // 指数退避
      const delay = Math.pow(2, attempt - 1) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}
