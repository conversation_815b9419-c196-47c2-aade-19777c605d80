'use client';

import { ReactNode, useState } from 'react';
import { X, BookO<PERSON>, Briefcase, Zap } from 'lucide-react';
import { PageLayout, PageContainer, PageHeader } from '@/components/layout/page-layout';
import { LinkedDatabaseNav } from './linked-database-nav';
import { EnhancedSearchBar } from './enhanced-search-bar';
import { DataGrid, DataStats } from './data-grid';
import { Pagination, PageSizeSelector } from './pagination';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface DatabaseLayoutProps {
  title: string;
  description?: string;
  currentPage: 'majors' | 'careers' | 'skills';
  children: ReactNode;
  
  // 搜索相关
  searchPlaceholder?: string;
  onSearch: (query: string) => void;
  
  // 筛选相关
  filterPanel?: ReactNode;
  showFilters?: boolean;
  
  // 排序相关
  sortOptions?: { value: string; label: string }[];
  currentSort?: string;
  onSortChange?: (sort: string) => void;
  
  // 视图相关
  currentView?: 'grid' | 'list';
  onViewChange?: (view: 'grid' | 'list') => void;
  showViewToggle?: boolean;
  
  // 分页相关
  currentPageNum?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  pageSize?: number;
  onPageSizeChange?: (size: number) => void;
  
  // 数据统计
  totalItems?: number;
  filteredItems?: number;
  loading?: boolean;
  
  className?: string;
}

export function DatabaseLayout({
  title,
  description,
  currentPage,
  children,
  searchPlaceholder,
  onSearch,
  filterPanel,
  showFilters = true,
  sortOptions,
  currentSort,
  onSortChange,
  currentView = 'grid',
  onViewChange,
  showViewToggle = true,
  currentPageNum = 1,
  totalPages = 1,
  onPageChange,
  pageSize = 20,
  onPageSizeChange,
  totalItems = 0,
  filteredItems,
  loading = false,
  className
}: DatabaseLayoutProps) {
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const handleFilterToggle = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  // 生成面包屑导航
  const getBreadcrumbItems = () => {
    const pageMap = {
      majors: { label: '专业库', icon: BookOpen },
      careers: { label: '职业库', icon: Briefcase },
      skills: { label: '技能库', icon: Zap }
    };

    return [
      {
        label: pageMap[currentPage].label,
        current: true
      }
    ];
  };

  // 生成页面路径
  const getCurrentPath = () => {
    const pathMap = {
      majors: '/majors',
      careers: '/careers',
      skills: '/skills'
    };
    return pathMap[currentPage];
  };

  return (
    <PageLayout
      backgroundPattern="gradient"
      className={className}
      showBreadcrumb={true}
      breadcrumbItems={getBreadcrumbItems()}
      currentPath={getCurrentPath()}
    >
      <PageContainer>
        {/* 页面标题 */}
        <PageHeader title={title} description={description} centered={false}>
          {/* 数据库导航 */}
          <div className="mb-6">
            <LinkedDatabaseNav currentPage={currentPage} />
          </div>
        </PageHeader>

        {/* 搜索和工具栏 */}
        <div className="mb-6">
          <EnhancedSearchBar
            placeholder={searchPlaceholder}
            onSearch={onSearch}
            onSortChange={onSortChange}
            onViewChange={onViewChange}
            onFilterToggle={showFilters ? handleFilterToggle : undefined}
            showFilters={showFilters}
            showSort={!!sortOptions}
            showViewToggle={showViewToggle}
            sortOptions={sortOptions}
            currentSort={currentSort}
            currentView={currentView}
          />
        </div>

        {/* 主要内容区域 */}
        <div className="flex gap-6">
          {/* 筛选面板 */}
          {showFilters && filterPanel && (
            <>
              {/* 桌面端侧边栏 */}
              <div className="hidden lg:block w-80 flex-shrink-0">
                <div className="sticky top-24">
                  {filterPanel}
                </div>
              </div>

              {/* 移动端抽屉 */}
              {isFilterOpen && (
                <div className="lg:hidden fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
                  <div className="fixed inset-y-0 left-0 w-80 bg-card border-r border-border shadow-lg animate-slide-right">
                    <div className="flex items-center justify-between p-4 border-b border-border">
                      <h3 className="text-lg font-semibold">筛选条件</h3>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setIsFilterOpen(false)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="p-4 overflow-y-auto h-full pb-20">
                      {filterPanel}
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {/* 内容区域 */}
          <div className="flex-1 min-w-0">
            {/* 数据统计 */}
            <div className="flex items-center justify-between mb-4">
              <DataStats
                total={totalItems}
                filtered={filteredItems}
                loading={loading}
              />
              
              {onPageSizeChange && (
                <PageSizeSelector
                  pageSize={pageSize}
                  onPageSizeChange={onPageSizeChange}
                />
              )}
            </div>

            {/* 数据网格 */}
            <div className="mb-8">
              <DataGrid
                loading={loading}
                empty={totalItems === 0}
                view={currentView}
                columns={currentView === 'grid' ? 3 : 1}
              >
                {children}
              </DataGrid>
            </div>

            {/* 分页 */}
            {onPageChange && totalPages > 1 && (
              <div className="flex justify-center">
                <Pagination
                  currentPage={currentPageNum}
                  totalPages={totalPages}
                  onPageChange={onPageChange}
                />
              </div>
            )}
          </div>
        </div>
      </PageContainer>
    </PageLayout>
  );
}

// 简化版数据库布局（用于详情页等）
interface SimpleDatabaseLayoutProps {
  title: string;
  description?: string;
  currentPage: 'majors' | 'careers' | 'skills';
  children: ReactNode;
  showBackButton?: boolean;
  onBack?: () => void;
  className?: string;
}

export function SimpleDatabaseLayout({
  title,
  description,
  currentPage,
  children,
  showBackButton = true,
  onBack,
  className
}: SimpleDatabaseLayoutProps) {
  return (
    <PageLayout backgroundPattern="gradient" className={className}>
      <PageContainer>
        {/* 数据库导航 */}
        <div className="mb-6">
          <LinkedDatabaseNav currentPage={currentPage} />
        </div>

        {/* 返回按钮 */}
        {showBackButton && (
          <div className="mb-4">
            <Button
              variant="ghost"
              onClick={onBack || (() => window.history.back())}
              className="hover-scale"
            >
              ← 返回
            </Button>
          </div>
        )}

        {/* 页面标题 */}
        <PageHeader title={title} description={description} centered={false} />

        {/* 内容 */}
        <div className="animate-fade-in">
          {children}
        </div>
      </PageContainer>
    </PageLayout>
  );
}
