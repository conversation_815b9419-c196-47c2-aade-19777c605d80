'use client';

import { Briefcase, TrendingUp, Users, DollarSign, GraduationCap, MapPin, Star, Bookmark, ExternalLink, Building, Award } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface CareerCardProps {
  career: {
    id: string;
    title: string;
    category: string;
    description: string;
    averageSalary?: {
      min?: number;
      max?: number;
      median?: number;
    };
    jobOutlook?: string;
    requiredSkills?: string[];
    workEnvironment?: string;
    educationLevel?: string;
    relatedMajors?: string[];
    companies?: string[];
    growthPath?: string[];
    tags?: string[];
  };
  view?: 'grid' | 'list';
  onClick?: () => void;
  className?: string;
}

export function EnhancedCareerCard({ career, view = 'grid', onClick, className }: CareerCardProps) {
  const getJobOutlookColor = (outlook?: string) => {
    if (!outlook) return 'text-muted-foreground';
    if (outlook.includes('高') || outlook.includes('优秀') || outlook.includes('增长')) return 'text-success';
    if (outlook.includes('中') || outlook.includes('良好') || outlook.includes('稳定')) return 'text-primary';
    return 'text-warning';
  };

  const formatSalary = (salary?: { min?: number; max?: number; median?: number }) => {
    if (!salary) return '薪资面议';
    
    const formatAmount = (amount: number) => {
      if (amount >= 10000) return `${(amount / 10000).toFixed(0)}万`;
      return `${amount}`;
    };

    if (salary.min && salary.max) {
      return `${formatAmount(salary.min)}-${formatAmount(salary.max)}`;
    } else if (salary.median) {
      return `${formatAmount(salary.median)}`;
    } else if (salary.min) {
      return `${formatAmount(salary.min)}+`;
    }
    return '薪资面议';
  };

  const getEducationColor = (level?: string) => {
    if (!level) return 'text-muted-foreground';
    if (level.includes('博士') || level.includes('研究生')) return 'text-purple-600';
    if (level.includes('本科') || level.includes('学士')) return 'text-primary';
    if (level.includes('专科') || level.includes('大专')) return 'text-success';
    return 'text-muted-foreground';
  };

  if (view === 'list') {
    return (
      <Card 
        className={cn(
          "hover-lift glass cursor-pointer transition-all duration-200",
          className
        )}
        onClick={onClick}
      >
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-r from-primary to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Briefcase className="w-5 h-5 text-white" />
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="text-lg font-semibold text-foreground truncate">
                    {career.title}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>{career.category}</span>
                    {career.workEnvironment && (
                      <>
                        <span>•</span>
                        <span>{career.workEnvironment}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                {career.description}
              </p>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-muted-foreground" />
                  <span className="text-muted-foreground">薪资</span>
                  <span className="font-medium text-foreground">
                    {formatSalary(career.averageSalary)}
                  </span>
                </div>
                
                {career.jobOutlook && (
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">前景</span>
                    <span className={cn("font-medium", getJobOutlookColor(career.jobOutlook))}>
                      {career.jobOutlook}
                    </span>
                  </div>
                )}
                
                {career.educationLevel && (
                  <div className="flex items-center space-x-2">
                    <GraduationCap className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">学历</span>
                    <span className={cn("font-medium", getEducationColor(career.educationLevel))}>
                      {career.educationLevel}
                    </span>
                  </div>
                )}
                
                {career.companies && career.companies.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <Building className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">企业</span>
                    <span className="font-medium text-foreground">
                      {career.companies.length}+
                    </span>
                  </div>
                )}
              </div>

              {/* 技能标签 */}
              {career.requiredSkills && career.requiredSkills.length > 0 && (
                <div className="mt-4">
                  <div className="flex flex-wrap gap-1">
                    {career.requiredSkills.slice(0, 4).map((skill, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                    {career.requiredSkills.length > 4 && (
                      <span className="px-2 py-1 bg-muted text-muted-foreground rounded-full text-xs">
                        +{career.requiredSkills.length - 4}
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2 ml-4">
              <Button variant="ghost" size="icon" className="hover-scale">
                <Bookmark className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" className="hover-scale">
                <ExternalLink className="w-4 h-4 mr-2" />
                详情
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      className={cn(
        "hover-lift glass cursor-pointer transition-all duration-200 group",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="w-12 h-12 bg-gradient-to-r from-primary to-purple-600 rounded-xl flex items-center justify-center mb-3 hover-scale">
            <Briefcase className="w-6 h-6 text-white" />
          </div>
          <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity hover-scale">
            <Bookmark className="w-4 h-4" />
          </Button>
        </div>
        
        <CardTitle className="text-lg text-foreground group-hover:text-primary transition-colors">
          {career.title}
        </CardTitle>
        
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <span>{career.category}</span>
          {career.workEnvironment && (
            <>
              <span>•</span>
              <span>{career.workEnvironment}</span>
            </>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground line-clamp-3">
          {career.description}
        </p>

        {/* 关键指标 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">薪资范围</span>
            </div>
            <span className="text-sm font-medium text-foreground">
              {formatSalary(career.averageSalary)}
            </span>
          </div>
          
          {career.jobOutlook && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">就业前景</span>
              </div>
              <span className={cn("text-sm font-medium", getJobOutlookColor(career.jobOutlook))}>
                {career.jobOutlook}
              </span>
            </div>
          )}
          
          {career.educationLevel && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <GraduationCap className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">学历要求</span>
              </div>
              <span className={cn("text-sm font-medium", getEducationColor(career.educationLevel))}>
                {career.educationLevel}
              </span>
            </div>
          )}
        </div>

        {/* 核心技能 */}
        {career.requiredSkills && career.requiredSkills.length > 0 && (
          <div>
            <div className="text-sm font-medium text-foreground mb-2">核心技能</div>
            <div className="flex flex-wrap gap-1">
              {career.requiredSkills.slice(0, 3).map((skill, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium"
                >
                  {skill}
                </span>
              ))}
              {career.requiredSkills.length > 3 && (
                <span className="px-2 py-1 bg-muted text-muted-foreground rounded-full text-xs">
                  +{career.requiredSkills.length - 3}
                </span>
              )}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-2">
          <Button size="sm" className="flex-1 hover-scale">
            查看详情
          </Button>
          <Button variant="outline" size="sm" className="hover-scale">
            对比
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
