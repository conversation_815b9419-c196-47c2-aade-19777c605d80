'use client';

import { useState } from 'react';
import { TrendingUp, DollarSign, Users, ChevronRight, Star, Briefcase } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface CareerMatch {
  career: string;
  matchScore: number;
  reasons: string[];
  salaryRange: string;
  outlook: 'excellent' | 'good' | 'average' | 'poor';
}

interface CareerRecommendationsProps {
  careers: CareerMatch[];
}

const OUTLOOK_CONFIG = {
  excellent: { text: '前景极佳', color: 'text-green-600', bgColor: 'bg-green-100' },
  good: { text: '前景良好', color: 'text-blue-600', bgColor: 'bg-blue-100' },
  average: { text: '前景一般', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  poor: { text: '前景较差', color: 'text-red-600', bgColor: 'bg-red-100' }
};

export function CareerRecommendations({ careers }: CareerRecommendationsProps) {
  const [selectedCareer, setSelectedCareer] = useState<string | null>(null);

  const getMatchColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getMatchBgColor = (score: number) => {
    if (score >= 90) return 'bg-green-100';
    if (score >= 80) return 'bg-blue-100';
    if (score >= 70) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">为你推荐的职业</h2>
        <p className="text-gray-600">基于你的性格特点，以下职业与你的匹配度最高</p>
      </div>

      <div className="grid gap-6">
        {careers.map((career, index) => {
          const isSelected = selectedCareer === career.career;
          const outlookConfig = OUTLOOK_CONFIG[career.outlook];
          
          return (
            <Card
              key={index}
              className={cn(
                "cursor-pointer transition-all duration-300 border-2",
                isSelected 
                  ? "border-blue-500 shadow-lg scale-102" 
                  : "border-gray-200 hover:border-blue-300 hover:shadow-md"
              )}
              onClick={() => setSelectedCareer(isSelected ? null : career.career)}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <Briefcase className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-gray-900">
                        {career.career}
                      </CardTitle>
                      <div className="flex items-center space-x-4 mt-1">
                        <div className="flex items-center">
                          <DollarSign className="w-4 h-4 text-gray-500 mr-1" />
                          <span className="text-sm text-gray-600">{career.salaryRange}</span>
                        </div>
                        <div className={cn(
                          "px-2 py-1 rounded-full text-xs font-medium",
                          outlookConfig.color,
                          outlookConfig.bgColor
                        )}>
                          {outlookConfig.text}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className={cn(
                      "text-2xl font-bold",
                      getMatchColor(career.matchScore)
                    )}>
                      {career.matchScore}%
                    </div>
                    <div className="text-sm text-gray-500">匹配度</div>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                {/* 匹配度进度条 */}
                <div className="mb-4">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={cn(
                        "h-2 rounded-full transition-all duration-1000",
                        career.matchScore >= 90 ? "bg-gradient-to-r from-green-500 to-emerald-500" :
                        career.matchScore >= 80 ? "bg-gradient-to-r from-blue-500 to-cyan-500" :
                        career.matchScore >= 70 ? "bg-gradient-to-r from-yellow-500 to-orange-500" :
                        "bg-gradient-to-r from-red-500 to-pink-500"
                      )}
                      style={{ width: `${career.matchScore}%` }}
                    />
                  </div>
                </div>

                {/* 匹配原因 */}
                <div className="space-y-2">
                  <h4 className="font-semibold text-gray-900 flex items-center">
                    <Star className="w-4 h-4 mr-2 text-yellow-500" />
                    匹配原因
                  </h4>
                  <div className="grid md:grid-cols-2 gap-2">
                    {career.reasons.map((reason, idx) => (
                      <div key={idx} className="flex items-center text-sm text-gray-600">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 flex-shrink-0" />
                        {reason}
                      </div>
                    ))}
                  </div>
                </div>

                {/* 展开的详细信息 */}
                {isSelected && (
                  <div className="mt-6 pt-6 border-t border-gray-200 animate-slide-up">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h5 className="font-semibold text-gray-900 mb-3">职业描述</h5>
                        <p className="text-sm text-gray-600 mb-4">
                          {career.career === '产品经理' && 
                            '负责产品的整体规划、设计和管理，协调各部门资源，确保产品成功上市并持续优化。'
                          }
                          {career.career === '市场营销专员' && 
                            '制定和执行市场营销策略，通过各种渠道推广产品和品牌，提升市场占有率。'
                          }
                          {career.career === 'UI/UX设计师' && 
                            '设计用户界面和用户体验，确保产品易用性和美观性，提升用户满意度。'
                          }
                        </p>
                        
                        <h5 className="font-semibold text-gray-900 mb-3">所需技能</h5>
                        <div className="flex flex-wrap gap-2">
                          {career.career === '产品经理' && 
                            ['产品规划', '数据分析', '项目管理', '用户研究'].map((skill, idx) => (
                              <span key={idx} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                                {skill}
                              </span>
                            ))
                          }
                          {career.career === '市场营销专员' && 
                            ['市场分析', '内容创作', '社交媒体', '数据分析'].map((skill, idx) => (
                              <span key={idx} className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                                {skill}
                              </span>
                            ))
                          }
                          {career.career === 'UI/UX设计师' && 
                            ['界面设计', '用户研究', '原型制作', '设计工具'].map((skill, idx) => (
                              <span key={idx} className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                                {skill}
                              </span>
                            ))
                          }
                        </div>
                      </div>
                      
                      <div>
                        <h5 className="font-semibold text-gray-900 mb-3">发展路径</h5>
                        <div className="space-y-2 text-sm text-gray-600">
                          {career.career === '产品经理' && (
                            <>
                              <div>初级产品经理 → 产品经理 → 高级产品经理 → 产品总监</div>
                              <div className="text-xs text-gray-500">预计发展周期：3-5年</div>
                            </>
                          )}
                          {career.career === '市场营销专员' && (
                            <>
                              <div>营销专员 → 营销主管 → 营销经理 → 营销总监</div>
                              <div className="text-xs text-gray-500">预计发展周期：3-5年</div>
                            </>
                          )}
                          {career.career === 'UI/UX设计师' && (
                            <>
                              <div>初级设计师 → 设计师 → 高级设计师 → 设计主管</div>
                              <div className="text-xs text-gray-500">预计发展周期：3-5年</div>
                            </>
                          )}
                        </div>
                        
                        <div className="mt-4">
                          <Button size="sm" className="w-full">
                            查看详细信息
                            <ChevronRight className="w-4 h-4 ml-2" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 更多推荐 */}
      <Card className="bg-gray-50 border-dashed border-2 border-gray-300">
        <CardContent className="p-6 text-center">
          <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            想要更多职业推荐？
          </h3>
          <p className="text-gray-600 mb-4">
            基于你的测评结果，我们还为你准备了更多相关职业选择
          </p>
          <Button variant="outline">
            查看更多职业
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
