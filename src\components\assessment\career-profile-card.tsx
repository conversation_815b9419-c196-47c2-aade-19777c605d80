'use client';

import { useState } from 'react';
import { Bookmark, BookmarkCheck, TrendingUp, MapPin, DollarSign, Users, ChevronRight, Lightbulb, Target, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface CareerProfile {
  id: string;
  title: string;
  description: string;
  matchScore: number;
  matchReasons: string[];
  salaryRange: string;
  growthPath: string[];
  requiredSkills: string[];
  workEnvironment: string;
  isBookmarked: boolean;
}

interface CareerProfileCardProps {
  profile: CareerProfile;
  onBookmark: () => void;
}

export function CareerProfileCard({ profile, onBookmark }: CareerProfileCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showGrowthPath, setShowGrowthPath] = useState(false);

  const getMatchColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getMatchBgColor = (score: number) => {
    if (score >= 90) return 'bg-green-100';
    if (score >= 80) return 'bg-blue-100';
    if (score >= 70) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getMatchLabel = (score: number) => {
    if (score >= 90) return '极高匹配';
    if (score >= 80) return '高度匹配';
    if (score >= 70) return '中等匹配';
    return '较低匹配';
  };

  return (
    <Card className={cn(
      "transition-all duration-300 border-2 hover:shadow-lg",
      isExpanded ? "border-blue-500 shadow-lg" : "border-gray-200 hover:border-blue-300"
    )}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <CardTitle className="text-xl text-gray-900">{profile.title}</CardTitle>
              <div className={cn(
                "px-2 py-1 rounded-full text-xs font-medium",
                getMatchColor(profile.matchScore),
                getMatchBgColor(profile.matchScore)
              )}>
                {getMatchLabel(profile.matchScore)}
              </div>
            </div>
            <CardDescription className="text-gray-600">
              {profile.description}
            </CardDescription>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="text-right">
              <div className={cn(
                "text-2xl font-bold",
                getMatchColor(profile.matchScore)
              )}>
                {profile.matchScore}%
              </div>
              <div className="text-xs text-gray-500">匹配度</div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onBookmark}
              className={cn(
                "p-2",
                profile.isBookmarked ? "text-yellow-600" : "text-gray-400"
              )}
            >
              {profile.isBookmarked ? (
                <BookmarkCheck className="w-4 h-4" />
              ) : (
                <Bookmark className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* 匹配度进度条 */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={cn(
                "h-2 rounded-full transition-all duration-1000",
                profile.matchScore >= 90 ? "bg-gradient-to-r from-green-500 to-emerald-500" :
                profile.matchScore >= 80 ? "bg-gradient-to-r from-blue-500 to-cyan-500" :
                profile.matchScore >= 70 ? "bg-gradient-to-r from-yellow-500 to-orange-500" :
                "bg-gradient-to-r from-red-500 to-pink-500"
              )}
              style={{ width: `${profile.matchScore}%` }}
            />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 基本信息 */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center text-gray-600">
            <DollarSign className="w-4 h-4 mr-2" />
            {profile.salaryRange}
          </div>
          <div className="flex items-center text-gray-600">
            <Users className="w-4 h-4 mr-2" />
            团队协作
          </div>
        </div>

        {/* 关键匹配点 */}
        <div>
          <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
            <Lightbulb className="w-4 h-4 mr-2 text-yellow-500" />
            关键匹配点
          </h4>
          <div className="space-y-1">
            {profile.matchReasons.slice(0, isExpanded ? profile.matchReasons.length : 2).map((reason, index) => (
              <div key={index} className="text-sm text-gray-600 flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0" />
                {reason}
              </div>
            ))}
          </div>
          {profile.matchReasons.length > 2 && !isExpanded && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(true)}
              className="text-blue-600 p-0 h-auto mt-1"
            >
              查看更多匹配点 <ChevronRight className="w-3 h-3 ml-1" />
            </Button>
          )}
        </div>

        {/* 展开的详细信息 */}
        {isExpanded && (
          <div className="space-y-4 pt-4 border-t border-gray-200 animate-slide-up">
            {/* 核心技能 */}
            <div>
              <h5 className="font-semibold text-gray-900 mb-2 flex items-center">
                <Target className="w-4 h-4 mr-2 text-purple-500" />
                核心技能
              </h5>
              <div className="flex flex-wrap gap-2">
                {profile.requiredSkills.map((skill, index) => (
                  <span 
                    key={index}
                    className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>

            {/* 工作环境 */}
            <div>
              <h5 className="font-semibold text-gray-900 mb-2 flex items-center">
                <MapPin className="w-4 h-4 mr-2 text-green-500" />
                工作环境
              </h5>
              <p className="text-sm text-gray-600">{profile.workEnvironment}</p>
            </div>

            {/* 成长路径预览 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h5 className="font-semibold text-gray-900 flex items-center">
                  <TrendingUp className="w-4 h-4 mr-2 text-blue-500" />
                  成长路径
                </h5>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowGrowthPath(!showGrowthPath)}
                  className="text-blue-600 p-0 h-auto"
                >
                  {showGrowthPath ? '收起' : '展开'}
                  <ChevronRight className={cn(
                    "w-3 h-3 ml-1 transition-transform",
                    showGrowthPath ? "rotate-90" : ""
                  )} />
                </Button>
              </div>
              
              {showGrowthPath ? (
                <div className="space-y-2">
                  {profile.growthPath.map((stage, index) => (
                    <div key={index} className="flex items-center text-sm">
                      <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold mr-3">
                        {index + 1}
                      </div>
                      <span className="text-gray-700">{stage}</span>
                      {index < profile.growthPath.length - 1 && (
                        <ChevronRight className="w-3 h-3 mx-2 text-gray-400" />
                      )}
                    </div>
                  ))}
                  <div className="flex items-center text-xs text-gray-500 mt-2">
                    <Clock className="w-3 h-3 mr-1" />
                    预计发展周期：3-5年
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  {profile.growthPath.slice(0, 3).map((stage, index) => (
                    <div key={index} className="flex items-center">
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {stage}
                      </span>
                      {index < 2 && <ChevronRight className="w-3 h-3 mx-1 text-gray-400" />}
                    </div>
                  ))}
                  {profile.growthPath.length > 3 && (
                    <span className="text-xs text-gray-500">...</span>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-4">
          <Button 
            size="sm" 
            className="flex-1"
            onClick={() => console.log('查看详细信息', profile.id)}
          >
            查看详情
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? '收起' : '展开'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
