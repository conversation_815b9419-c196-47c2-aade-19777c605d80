'use client';

import { useState } from 'react';
import { X, Sparkles, Gift, Megaphone, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface AnnouncementBarProps {
  message: string;
  type?: 'info' | 'success' | 'warning' | 'promotion';
  actionText?: string;
  actionHref?: string;
  onAction?: () => void;
  dismissible?: boolean;
  className?: string;
}

export function AnnouncementBar({
  message,
  type = 'info',
  actionText,
  actionHref,
  onAction,
  dismissible = true,
  className
}: AnnouncementBarProps) {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-success/10 border-success/20',
          text: 'text-success-foreground',
          icon: Sparkles
        };
      case 'warning':
        return {
          bg: 'bg-warning/10 border-warning/20',
          text: 'text-warning-foreground',
          icon: Info
        };
      case 'promotion':
        return {
          bg: 'bg-gradient-to-r from-primary/10 to-purple-600/10 border-primary/20',
          text: 'text-foreground',
          icon: Gift
        };
      default:
        return {
          bg: 'bg-muted/50 border-border',
          text: 'text-foreground',
          icon: Megaphone
        };
    }
  };

  const { bg, text, icon: Icon } = getTypeStyles();

  return (
    <div className={cn(
      "relative border-b transition-all duration-300 animate-slide-down",
      bg,
      className
    )}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-3">
          <div className="flex items-center space-x-3 flex-1">
            <Icon className={cn("w-4 h-4 flex-shrink-0", text)} />
            <p className={cn("text-sm font-medium", text)}>
              {message}
            </p>
          </div>

          <div className="flex items-center space-x-3">
            {(actionText || onAction) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onAction}
                asChild={!!actionHref}
                className={cn(
                  "text-xs font-medium hover-scale",
                  type === 'promotion' ? "text-primary hover:text-primary-hover" : text
                )}
              >
                {actionHref ? (
                  <a href={actionHref}>{actionText}</a>
                ) : (
                  actionText
                )}
              </Button>
            )}

            {dismissible && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsVisible(false)}
                className={cn("h-6 w-6 hover-scale", text)}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// 预设的公告类型
export const AnnouncementPresets = {
  newFeature: (feature: string) => ({
    message: `🎉 新功能上线：${feature}，快来体验吧！`,
    type: 'success' as const,
    actionText: '立即体验',
    dismissible: true
  }),

  promotion: (title: string, discount?: string) => ({
    message: `🎁 ${title}${discount ? ` - ${discount}` : ''}`,
    type: 'promotion' as const,
    actionText: '了解详情',
    dismissible: true
  }),

  maintenance: (time: string) => ({
    message: `⚠️ 系统维护通知：${time} 期间服务可能暂时中断`,
    type: 'warning' as const,
    actionText: '查看详情',
    dismissible: false
  }),

  update: (version: string) => ({
    message: `✨ 系统已更新至 ${version}，新增多项功能优化`,
    type: 'info' as const,
    actionText: '查看更新日志',
    dismissible: true
  })
};

// 多个公告的轮播组件
interface AnnouncementCarouselProps {
  announcements: AnnouncementBarProps[];
  autoRotate?: boolean;
  rotateInterval?: number;
  className?: string;
}

export function AnnouncementCarousel({
  announcements,
  autoRotate = true,
  rotateInterval = 5000,
  className
}: AnnouncementCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [visibleAnnouncements, setVisibleAnnouncements] = useState(announcements);

  // 自动轮播
  useState(() => {
    if (!autoRotate || visibleAnnouncements.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % visibleAnnouncements.length);
    }, rotateInterval);

    return () => clearInterval(interval);
  });

  const handleDismiss = (index: number) => {
    const newAnnouncements = visibleAnnouncements.filter((_, i) => i !== index);
    setVisibleAnnouncements(newAnnouncements);
    
    if (currentIndex >= newAnnouncements.length) {
      setCurrentIndex(Math.max(0, newAnnouncements.length - 1));
    }
  };

  if (visibleAnnouncements.length === 0) return null;

  const currentAnnouncement = visibleAnnouncements[currentIndex];

  return (
    <div className={className}>
      <AnnouncementBar
        {...currentAnnouncement}
        onAction={() => {
          currentAnnouncement.onAction?.();
          if (currentAnnouncement.actionHref) {
            window.location.href = currentAnnouncement.actionHref;
          }
        }}
        dismissible={currentAnnouncement.dismissible !== false}
      />
      
      {/* 指示器 */}
      {visibleAnnouncements.length > 1 && (
        <div className="flex justify-center space-x-1 py-2 bg-muted/30">
          {visibleAnnouncements.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-200 hover-scale",
                index === currentIndex ? "bg-primary" : "bg-muted-foreground/30"
              )}
            />
          ))}
        </div>
      )}
    </div>
  );
}
