'use client';

import { useState, useRef, useEffect } from 'react';
import { Bo<PERSON>, User, Send, Loader2, <PERSON>rkles, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  type: 'bot' | 'user';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

interface ConversationInterfaceProps {
  onComplete: () => void;
}

export function ConversationInterface({ onComplete }: ConversationInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const conversationFlow = [
    {
      message: '你好！我是你的AI职业顾问 🤖 很高兴认识你！让我们通过轻松的对话来了解你的职业兴趣吧。首先，能告诉我你平时最喜欢做什么吗？',
      suggestions: ['阅读学习', '运动健身', '编程开发', '设计创作', '音乐艺术', '社交聊天']
    },
    {
      message: '很棒！看起来你是个很有趣的人 😊 那么在学习方面，你最擅长或者最感兴趣的科目是什么呢？',
      suggestions: ['数学', '物理', '化学', '生物', '语文', '英语', '历史', '地理', '政治', '计算机']
    },
    {
      message: '了解了！你的学习能力很不错呢 👍 未来工作的话，你比较倾向于在哪些城市发展？',
      suggestions: ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '不限地区']
    },
    {
      message: '好的！关于工作方式，你更喜欢哪种类型呢？',
      suggestions: ['独立工作', '团队协作', '领导他人', '创新研究', '稳定执行', '灵活自由']
    },
    {
      message: '太好了！最后一个问题：你希望未来的工作能给你带来什么？',
      suggestions: ['高收入', '成就感', '工作稳定', '自由时间', '社会价值', '个人成长']
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      addBotMessage(conversationFlow[0].message, conversationFlow[0].suggestions);
    }, 1000);
  }, []);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    setProgress((currentStep / conversationFlow.length) * 100);
  }, [currentStep]);

  const addBotMessage = (content: string, suggestions?: string[]) => {
    setIsTyping(true);
    setTimeout(() => {
      const message: Message = {
        id: `bot-${Date.now()}`,
        type: 'bot',
        content,
        timestamp: new Date(),
        suggestions
      };
      setMessages(prev => [...prev, message]);
      setIsTyping(false);
    }, 1500 + Math.random() * 1000); // 模拟打字延迟
  };

  const addUserMessage = (content: string) => {
    const message: Message = {
      id: `user-${Date.now()}`,
      type: 'user',
      content,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
    setCurrentInput('');

    // 进入下一步
    const nextStep = currentStep + 1;
    setCurrentStep(nextStep);

    if (nextStep < conversationFlow.length) {
      setTimeout(() => {
        addBotMessage(conversationFlow[nextStep].message, conversationFlow[nextStep].suggestions);
      }, 1000);
    } else {
      // 对话完成
      setTimeout(() => {
        addBotMessage('太棒了！我已经收集到足够的信息了 ✨ 让我为你生成个性化的职业画像...');
        setTimeout(() => {
          onComplete();
        }, 2000);
      }, 1000);
    }
  };

  const handleSend = () => {
    if (currentInput.trim()) {
      addUserMessage(currentInput.trim());
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    addUserMessage(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* 进度条 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-foreground">对话进度</span>
          <span className="text-sm text-muted-foreground">{currentStep}/{conversationFlow.length}</span>
        </div>
        <div className="w-full bg-muted rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-primary to-purple-600 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* 对话区域 */}
      <Card className="glass">
        <CardContent className="p-0">
          <div className="h-96 overflow-y-auto p-6 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex items-start space-x-3 animate-slide-up",
                  message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                )}
              >
                {/* 头像 */}
                <div className={cn(
                  "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                  message.type === 'bot' 
                    ? 'bg-gradient-to-r from-primary to-purple-600' 
                    : 'bg-gradient-to-r from-green-500 to-emerald-500'
                )}>
                  {message.type === 'bot' ? (
                    <Bot className="w-4 h-4 text-white" />
                  ) : (
                    <User className="w-4 h-4 text-white" />
                  )}
                </div>

                {/* 消息内容 */}
                <div className={cn(
                  "flex-1 max-w-xs sm:max-w-md",
                  message.type === 'user' ? 'text-right' : ''
                )}>
                  <div className={cn(
                    "inline-block p-3 rounded-2xl text-sm",
                    message.type === 'bot'
                      ? 'bg-muted text-foreground'
                      : 'bg-primary text-primary-foreground'
                  )}>
                    {message.content}
                  </div>
                  
                  {/* 建议按钮 */}
                  {message.type === 'bot' && message.suggestions && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {message.suggestions.map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => handleSuggestionClick(suggestion)}
                          className="text-xs hover-scale glass"
                        >
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}

            {/* 打字指示器 */}
            {isTyping && (
              <div className="flex items-start space-x-3 animate-fade-in">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-primary to-purple-600 flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div className="bg-muted p-3 rounded-2xl">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* 输入区域 */}
          <div className="border-t border-border p-4">
            <div className="flex items-center space-x-2">
              <Input
                ref={inputRef}
                value={currentInput}
                onChange={(e) => setCurrentInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入你的回答..."
                className="flex-1"
                disabled={isTyping}
              />
              <Button
                onClick={handleSend}
                disabled={!currentInput.trim() || isTyping}
                className="hover-scale"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
