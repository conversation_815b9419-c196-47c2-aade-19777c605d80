'use client';

import { useState, useEffect } from 'react';
import { TrendingUp, Users, BookOpen, Building, Award, Target } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const STATS_DATA = [
  {
    title: '热门专业排行',
    icon: TrendingUp,
    color: 'from-blue-500 to-cyan-500',
    data: [
      { name: '计算机科学与技术', value: 95, trend: '+5%' },
      { name: '人工智能', value: 92, trend: '+12%' },
      { name: '数据科学与大数据', value: 88, trend: '+8%' },
      { name: '软件工程', value: 85, trend: '+3%' },
      { name: '网络安全', value: 82, trend: '+15%' }
    ]
  },
  {
    title: '就业前景指数',
    icon: Building,
    color: 'from-green-500 to-emerald-500',
    data: [
      { name: '互联网/科技', value: 98, trend: '+8%' },
      { name: '金融/投资', value: 89, trend: '+2%' },
      { name: '医疗健康', value: 87, trend: '+6%' },
      { name: '教育培训', value: 84, trend: '+4%' },
      { name: '新能源', value: 91, trend: '+18%' }
    ]
  },
  {
    title: '薪资水平统计',
    icon: Award,
    color: 'from-purple-500 to-pink-500',
    data: [
      { name: '算法工程师', value: '35K', trend: '+12%' },
      { name: '产品经理', value: '28K', trend: '+8%' },
      { name: '数据分析师', value: '22K', trend: '+15%' },
      { name: 'UI设计师', value: '18K', trend: '+6%' },
      { name: '运营专员', value: '15K', trend: '+4%' }
    ]
  }
];

function AnimatedNumber({ value, suffix = '' }: { value: number | string; suffix?: string }) {
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    if (typeof value === 'number') {
      let start = 0;
      const end = value;
      const duration = 2000;
      const increment = end / (duration / 16);

      const timer = setInterval(() => {
        start += increment;
        if (start >= end) {
          setDisplayValue(end);
          clearInterval(timer);
        } else {
          setDisplayValue(Math.floor(start));
        }
      }, 16);

      return () => clearInterval(timer);
    }
  }, [value]);

  if (typeof value === 'string') {
    return <span>{value}{suffix}</span>;
  }

  return <span>{displayValue}{suffix}</span>;
}

function ProgressBar({ value, color }: { value: number; color: string }) {
  return (
    <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
      <div
        className={`h-full bg-gradient-to-r ${color} transition-all duration-1000 ease-out`}
        style={{ width: `${value}%` }}
      />
    </div>
  );
}

export function StatsSection() {
  return (
    <section className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">实时数据洞察</h2>
        <p className="text-lg text-gray-600">基于大数据分析的行业趋势和就业前景</p>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {STATS_DATA.map((section, index) => {
          const Icon = section.icon;
          
          return (
            <Card key={index} className="bg-white/60 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-gray-900">
                    {section.title}
                  </CardTitle>
                  <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${section.color} flex items-center justify-center`}>
                    <Icon className="w-5 h-5 text-white" />
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {section.data.map((item, idx) => (
                  <div key={idx} className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="font-medium text-gray-700">{item.name}</span>
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold text-gray-900">
                          <AnimatedNumber 
                            value={item.value} 
                            suffix={typeof item.value === 'number' ? '' : ''} 
                          />
                        </span>
                        <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                          {item.trend}
                        </span>
                      </div>
                    </div>
                    {typeof item.value === 'number' && (
                      <ProgressBar value={item.value} color={section.color} />
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 全局统计 */}
      <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Users className="w-8 h-8 text-white" />
          </div>
          <div className="text-3xl font-bold text-gray-900 mb-2">
            <AnimatedNumber value={500000} suffix="+" />
          </div>
          <div className="text-sm text-gray-600">注册用户</div>
        </div>

        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <BookOpen className="w-8 h-8 text-white" />
          </div>
          <div className="text-3xl font-bold text-gray-900 mb-2">
            <AnimatedNumber value={1200} suffix="+" />
          </div>
          <div className="text-sm text-gray-600">专业覆盖</div>
        </div>

        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Building className="w-8 h-8 text-white" />
          </div>
          <div className="text-3xl font-bold text-gray-900 mb-2">
            <AnimatedNumber value={5000} suffix="+" />
          </div>
          <div className="text-sm text-gray-600">职业数据</div>
        </div>

        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Target className="w-8 h-8 text-white" />
          </div>
          <div className="text-3xl font-bold text-gray-900 mb-2">
            <AnimatedNumber value={98} suffix="%" />
          </div>
          <div className="text-sm text-gray-600">匹配准确率</div>
        </div>
      </div>
    </section>
  );
}
