'use client';

import { useEffect, useRef } from 'react';

interface Trait {
  name: string;
  score: number;
  description: string;
}

interface PersonalityChartProps {
  traits: Trait[];
}

export function PersonalityChart({ traits }: PersonalityChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置画布大小
    const size = 300;
    canvas.width = size;
    canvas.height = size;

    const centerX = size / 2;
    const centerY = size / 2;
    const radius = size / 2 - 40;

    // 清空画布
    ctx.clearRect(0, 0, size, size);

    // 绘制背景网格
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;

    // 绘制同心圆
    for (let i = 1; i <= 5; i++) {
      ctx.beginPath();
      ctx.arc(centerX, centerY, (radius * i) / 5, 0, 2 * Math.PI);
      ctx.stroke();
    }

    // 绘制轴线
    const angleStep = (2 * Math.PI) / traits.length;
    
    traits.forEach((_, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(x, y);
      ctx.stroke();
    });

    // 绘制数据多边形
    ctx.beginPath();
    ctx.strokeStyle = '#3b82f6';
    ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';
    ctx.lineWidth = 2;

    traits.forEach((trait, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const distance = (trait.score / 100) * radius;
      const x = centerX + Math.cos(angle) * distance;
      const y = centerY + Math.sin(angle) * distance;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    // 绘制数据点
    ctx.fillStyle = '#3b82f6';
    traits.forEach((trait, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const distance = (trait.score / 100) * radius;
      const x = centerX + Math.cos(angle) * distance;
      const y = centerY + Math.sin(angle) * distance;
      
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();
    });

    // 绘制标签
    ctx.fillStyle = '#374151';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    traits.forEach((trait, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const labelDistance = radius + 20;
      const x = centerX + Math.cos(angle) * labelDistance;
      const y = centerY + Math.sin(angle) * labelDistance;
      
      // 调整文本对齐方式
      if (Math.cos(angle) > 0.1) {
        ctx.textAlign = 'left';
      } else if (Math.cos(angle) < -0.1) {
        ctx.textAlign = 'right';
      } else {
        ctx.textAlign = 'center';
      }
      
      ctx.fillText(trait.name, x, y);
    });

  }, [traits]);

  return (
    <div className="space-y-4">
      <div className="flex justify-center">
        <canvas
          ref={canvasRef}
          className="max-w-full h-auto"
          style={{ maxWidth: '300px', maxHeight: '300px' }}
        />
      </div>
      
      {/* 特质详情 */}
      <div className="space-y-3">
        {traits.map((trait, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium text-gray-700">{trait.name}</span>
                <span className="text-sm font-semibold text-blue-600">{trait.score}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-1000"
                  style={{ width: `${trait.score}%` }}
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">{trait.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
