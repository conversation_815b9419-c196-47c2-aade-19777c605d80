import { supabase, Major } from '@/lib/supabase';

export interface MajorSearchParams {
  query?: string;
  category?: string[];
  employmentRateMin?: number;
  employmentRateMax?: number;
  salaryMin?: number;
  salaryMax?: number;
  jobOutlook?: string[];
  universityType?: string[];
  tags?: string[];
  limit?: number;
  offset?: number;
}

export class MajorsAPI {
  static async searchMajors(params: MajorSearchParams = {}): Promise<{
    data: Major[];
    total: number;
  }> {
    let query = supabase
      .from('majors')
      .select('*', { count: 'exact' });

    // 文本搜索
    if (params.query) {
      query = query.or(`name.ilike.%${params.query}%,description.ilike.%${params.query}%,tags.cs.{${params.query}}`);
    }

    // 分类筛选
    if (params.category && params.category.length > 0) {
      query = query.in('category', params.category);
    }

    // 就业率范围
    if (params.employmentRateMin !== undefined) {
      query = query.gte('employment_rate', params.employmentRateMin);
    }
    if (params.employmentRateMax !== undefined) {
      query = query.lte('employment_rate', params.employmentRateMax);
    }

    // 薪资范围
    if (params.salaryMin !== undefined) {
      query = query.gte('average_starting_salary', params.salaryMin);
    }
    if (params.salaryMax !== undefined) {
      query = query.lte('average_starting_salary', params.salaryMax);
    }

    // 就业前景
    if (params.jobOutlook && params.jobOutlook.length > 0) {
      query = query.in('job_outlook', params.jobOutlook);
    }

    // 标签筛选
    if (params.tags && params.tags.length > 0) {
      query = query.overlaps('tags', params.tags);
    }

    // 分页
    if (params.limit) {
      query = query.limit(params.limit);
    }
    if (params.offset) {
      query = query.range(params.offset, params.offset + (params.limit || 10) - 1);
    }

    // 排序
    query = query.order('employment_rate', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to search majors: ${error.message}`);
    }

    return {
      data: data || [],
      total: count || 0
    };
  }

  static async getMajorById(id: string): Promise<Major | null> {
    const { data, error } = await supabase
      .from('majors')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get major: ${error.message}`);
    }

    return data;
  }

  static async getMajorsByCategory(category: string): Promise<Major[]> {
    const { data, error } = await supabase
      .from('majors')
      .select('*')
      .eq('category', category)
      .order('employment_rate', { ascending: false });

    if (error) {
      throw new Error(`Failed to get majors by category: ${error.message}`);
    }

    return data || [];
  }

  static async getPopularMajors(limit: number = 10): Promise<Major[]> {
    const { data, error } = await supabase
      .from('majors')
      .select('*')
      .order('employment_rate', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get popular majors: ${error.message}`);
    }

    return data || [];
  }

  static async getRelatedMajors(majorId: string, limit: number = 5): Promise<Major[]> {
    // 首先获取当前专业信息
    const currentMajor = await this.getMajorById(majorId);
    if (!currentMajor) {
      return [];
    }

    // 基于相同类别和相似标签推荐相关专业
    const { data, error } = await supabase
      .from('majors')
      .select('*')
      .neq('id', majorId)
      .or(`category.eq.${currentMajor.category},tags.ov.{${currentMajor.tags.join(',')}}`)
      .order('employment_rate', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get related majors: ${error.message}`);
    }

    return data || [];
  }

  static async getMajorCategories(): Promise<Array<{ category: string; count: number }>> {
    const { data, error } = await supabase
      .from('majors')
      .select('category')
      .not('category', 'is', null);

    if (error) {
      throw new Error(`Failed to get major categories: ${error.message}`);
    }

    // 统计每个分类的数量
    const categoryCount: Record<string, number> = {};
    data?.forEach(item => {
      categoryCount[item.category] = (categoryCount[item.category] || 0) + 1;
    });

    return Object.entries(categoryCount).map(([category, count]) => ({
      category,
      count
    }));
  }

  static async searchMajorsBySkills(skills: string[]): Promise<Major[]> {
    if (skills.length === 0) {
      return [];
    }

    const { data, error } = await supabase
      .from('majors')
      .select('*')
      .overlaps('related_skills', skills)
      .order('employment_rate', { ascending: false });

    if (error) {
      throw new Error(`Failed to search majors by skills: ${error.message}`);
    }

    return data || [];
  }

  static async searchMajorsByCareers(careers: string[]): Promise<Major[]> {
    if (careers.length === 0) {
      return [];
    }

    const { data, error } = await supabase
      .from('majors')
      .select('*')
      .overlaps('related_careers', careers)
      .order('employment_rate', { ascending: false });

    if (error) {
      throw new Error(`Failed to search majors by careers: ${error.message}`);
    }

    return data || [];
  }
}
