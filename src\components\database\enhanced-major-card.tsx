'use client';

import { Book<PERSON>pen, TrendingUp, Users, Award, GraduationCap, MapPin, DollarSign, Star, Bookmark, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface MajorCardProps {
  major: {
    id: string;
    name: string;
    code?: string;
    category: string;
    subcategory?: string;
    description: string;
    coreSubjects?: string[];
    employmentRate?: number;
    averageStartingSalary?: number;
    jobOutlook?: string;
    relatedCareers?: string[];
    relatedSkills?: string[];
    universities?: string[];
    admissionScore?: {
      min?: number;
      avg?: number;
      max?: number;
    };
    tags?: string[];
  };
  view?: 'grid' | 'list';
  onClick?: () => void;
  className?: string;
}

export function EnhancedMajorCard({ major, view = 'grid', onClick, className }: MajorCardProps) {
  const getEmploymentRateColor = (rate?: number) => {
    if (!rate) return 'text-muted-foreground';
    if (rate >= 90) return 'text-success';
    if (rate >= 80) return 'text-primary';
    if (rate >= 70) return 'text-warning';
    return 'text-destructive';
  };

  const getJobOutlookColor = (outlook?: string) => {
    if (!outlook) return 'text-muted-foreground';
    if (outlook.includes('高') || outlook.includes('优秀')) return 'text-success';
    if (outlook.includes('中') || outlook.includes('良好')) return 'text-primary';
    return 'text-warning';
  };

  const formatSalary = (salary?: number) => {
    if (!salary) return '暂无数据';
    if (salary >= 10000) return `${(salary / 10000).toFixed(1)}万`;
    return `${salary}元`;
  };

  if (view === 'list') {
    return (
      <Card 
        className={cn(
          "hover-lift glass cursor-pointer transition-all duration-200",
          className
        )}
        onClick={onClick}
      >
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-r from-primary to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <BookOpen className="w-5 h-5 text-white" />
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="text-lg font-semibold text-foreground truncate">
                    {major.name}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>{major.category}</span>
                    {major.subcategory && (
                      <>
                        <span>•</span>
                        <span>{major.subcategory}</span>
                      </>
                    )}
                    {major.code && (
                      <>
                        <span>•</span>
                        <span>{major.code}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                {major.description}
              </p>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                {major.employmentRate && (
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">就业率</span>
                    <span className={cn("font-medium", getEmploymentRateColor(major.employmentRate))}>
                      {major.employmentRate}%
                    </span>
                  </div>
                )}
                
                {major.averageStartingSalary && (
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">起薪</span>
                    <span className="font-medium text-foreground">
                      {formatSalary(major.averageStartingSalary)}
                    </span>
                  </div>
                )}
                
                {major.jobOutlook && (
                  <div className="flex items-center space-x-2">
                    <Award className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">前景</span>
                    <span className={cn("font-medium", getJobOutlookColor(major.jobOutlook))}>
                      {major.jobOutlook}
                    </span>
                  </div>
                )}
                
                {major.admissionScore?.avg && (
                  <div className="flex items-center space-x-2">
                    <GraduationCap className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">录取分</span>
                    <span className="font-medium text-foreground">
                      {major.admissionScore.avg}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2 ml-4">
              <Button variant="ghost" size="icon" className="hover-scale">
                <Bookmark className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" className="hover-scale">
                <ExternalLink className="w-4 h-4 mr-2" />
                详情
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      className={cn(
        "hover-lift glass cursor-pointer transition-all duration-200 group",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="w-12 h-12 bg-gradient-to-r from-primary to-purple-600 rounded-xl flex items-center justify-center mb-3 hover-scale">
            <BookOpen className="w-6 h-6 text-white" />
          </div>
          <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity hover-scale">
            <Bookmark className="w-4 h-4" />
          </Button>
        </div>
        
        <CardTitle className="text-lg text-foreground group-hover:text-primary transition-colors">
          {major.name}
        </CardTitle>
        
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <span>{major.category}</span>
          {major.subcategory && (
            <>
              <span>•</span>
              <span>{major.subcategory}</span>
            </>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground line-clamp-3">
          {major.description}
        </p>

        {/* 关键指标 */}
        <div className="space-y-3">
          {major.employmentRate && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">就业率</span>
              </div>
              <span className={cn("text-sm font-medium", getEmploymentRateColor(major.employmentRate))}>
                {major.employmentRate}%
              </span>
            </div>
          )}
          
          {major.averageStartingSalary && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">平均起薪</span>
              </div>
              <span className="text-sm font-medium text-foreground">
                {formatSalary(major.averageStartingSalary)}
              </span>
            </div>
          )}
          
          {major.jobOutlook && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Award className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">就业前景</span>
              </div>
              <span className={cn("text-sm font-medium", getJobOutlookColor(major.jobOutlook))}>
                {major.jobOutlook}
              </span>
            </div>
          )}
        </div>

        {/* 标签 */}
        {major.tags && major.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {major.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium"
              >
                {tag}
              </span>
            ))}
            {major.tags.length > 3 && (
              <span className="px-2 py-1 bg-muted text-muted-foreground rounded-full text-xs">
                +{major.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-2">
          <Button size="sm" className="flex-1 hover-scale">
            查看详情
          </Button>
          <Button variant="outline" size="sm" className="hover-scale">
            对比
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
