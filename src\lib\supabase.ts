import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://vatolrrrusqsqfofyosl.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZhdG9scnJydXNxc3Fmb2Z5b3NsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzY1MDIsImV4cCI6MjA2ODg1MjUwMn0.BPn4Mmy0dRoQUN4DMcpM5rITEYILjsclMg3Nlyr6iKI';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 数据库类型定义
export interface User {
  id: string;
  email: string;
  username?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  user_id: string;
  grade?: string;
  province?: string;
  city?: string;
  school?: string;
  subject_combination?: string;
  target_score?: number;
  interests?: string[];
  personality_type?: string;
  career_goals?: string[];
  created_at: string;
  updated_at: string;
}

export interface Major {
  id: string;
  name: string;
  code: string;
  category: string;
  subcategory: string;
  description: string;
  core_subjects: string[];
  employment_rate: number;
  average_starting_salary: number;
  job_outlook: string;
  related_careers: string[];
  related_skills: string[];
  universities: string[];
  admission_score_min: number;
  admission_score_avg: number;
  admission_score_max: number;
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface Career {
  id: string;
  title: string;
  category: string;
  description: string;
  salary_min: number;
  salary_max: number;
  salary_median: number;
  job_outlook: string;
  required_skills: string[];
  work_environment: string;
  education_level: string[];
  related_majors: string[];
  companies: string[];
  growth_path: string[];
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface Skill {
  id: string;
  name: string;
  category: string;
  description: string;
  level: string;
  demand_level: string;
  related_careers: string[];
  learning_path: any;
  learning_resources: any;
  prerequisites: string[];
  certifications: string[];
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface Article {
  id: string;
  title: string;
  summary: string;
  content?: string;
  category: string;
  tags: string[];
  author: string;
  image_url?: string;
  read_count: number;
  is_hot: boolean;
  published_at: string;
  created_at: string;
  updated_at: string;
}

export interface UserAssessment {
  id: string;
  user_id: string;
  session_id: string;
  assessment_type: string;
  conversation_data: any;
  results: any;
  career_recommendations: any;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserFavorite {
  id: string;
  user_id: string;
  item_type: 'major' | 'career' | 'skill';
  item_id: string;
  created_at: string;
}
