'use client';

import { <PERSON>, <PERSON>, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/contexts/theme-context';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  variant?: 'default' | 'compact';
  className?: string;
}

export function ThemeToggle({ variant = 'default', className }: ThemeToggleProps) {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    const themes = ['light', 'dark', 'system'] as const;
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const getIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun className="h-4 w-4" />;
      case 'dark':
        return <Moon className="h-4 w-4" />;
      case 'system':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Sun className="h-4 w-4" />;
    }
  };

  const getLabel = () => {
    switch (theme) {
      case 'light':
        return '亮色模式';
      case 'dark':
        return '暗色模式';
      case 'system':
        return '跟随系统';
      default:
        return '亮色模式';
    }
  };

  if (variant === 'compact') {
    return (
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleTheme}
        className={cn(
          "relative h-9 w-9 rounded-full transition-all duration-200 hover:bg-accent",
          className
        )}
        title={getLabel()}
      >
        <div className="transition-transform duration-200 hover:scale-110">
          {getIcon()}
        </div>
      </Button>
    );
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      className={cn(
        "flex items-center space-x-2 transition-all duration-200 hover:bg-accent",
        className
      )}
    >
      <div className="transition-transform duration-200 hover:scale-110">
        {getIcon()}
      </div>
      <span className="text-sm font-medium">{getLabel()}</span>
    </Button>
  );
}

// 主题选择器组件（下拉菜单版本）
export function ThemeSelector({ className }: { className?: string }) {
  const { theme, setTheme } = useTheme();

  const themes = [
    { value: 'light', label: '亮色模式', icon: Sun },
    { value: 'dark', label: '暗色模式', icon: Moon },
    { value: 'system', label: '跟随系统', icon: Monitor },
  ] as const;

  return (
    <div className={cn("flex items-center space-x-1 rounded-lg bg-muted p-1", className)}>
      {themes.map(({ value, label, icon: Icon }) => (
        <Button
          key={value}
          variant={theme === value ? "default" : "ghost"}
          size="sm"
          onClick={() => setTheme(value)}
          className={cn(
            "flex items-center space-x-2 transition-all duration-200",
            theme === value 
              ? "bg-background text-foreground shadow-sm" 
              : "hover:bg-background/50"
          )}
        >
          <Icon className="h-4 w-4" />
          <span className="text-xs font-medium">{label}</span>
        </Button>
      ))}
    </div>
  );
}
