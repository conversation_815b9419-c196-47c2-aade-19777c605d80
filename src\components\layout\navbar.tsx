'use client';

import { useState, useEffect, useRef } from 'react';
import { Menu, X, ChevronDown, Sparkles, Search, Bell, User, Settings, LogOut, BookOpen, Briefcase, Zap, BarChart3, Home } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { cn } from '@/lib/utils';
import { APP_CONFIG, ROUTES } from '@/lib/constants';

interface NavItem {
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  children?: NavItem[];
  badge?: string;
  description?: string;
}

const navigation: NavItem[] = [
  {
    label: '首页',
    href: ROUTES.HOME,
    icon: Home,
    description: '返回首页'
  },
  {
    label: 'AI测评',
    href: ROUTES.ASSESSMENT,
    icon: Sparkles,
    badge: 'AI',
    description: '智能职业测评'
  },
  {
    label: '专业库',
    href: ROUTES.MAJORS,
    icon: BookOpen,
    description: '探索专业信息'
  },
  {
    label: '职业库',
    href: ROUTES.CAREERS,
    icon: Briefcase,
    description: '了解职业发展'
  },
  {
    label: '技能库',
    href: '/skills',
    icon: Zap,
    badge: 'NEW',
    description: '提升核心技能'
  },
  {
    label: '就业数据',
    href: ROUTES.EMPLOYMENT,
    icon: BarChart3,
    description: '查看就业趋势'
  },
];

interface NavbarProps {
  className?: string;
  variant?: 'default' | 'transparent';
  showSearch?: boolean;
  currentPath?: string;
}

export function Navbar({ className, variant = 'default', showSearch = true, currentPath }: NavbarProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [notifications] = useState(3); // 模拟通知数量
  const searchRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsSearchOpen(false);
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (query: string) => {
    console.log('搜索:', query);
    // 这里可以添加搜索逻辑
  };

  const isTransparent = variant === 'transparent' && !isScrolled;

  return (
    <nav
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-300",
        isTransparent
          ? "bg-transparent"
          : "bg-background/80 backdrop-blur-md border-b border-border",
        isScrolled && "shadow-sm",
        className
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <a href={ROUTES.HOME} className="flex items-center space-x-3 group">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-primary to-purple-600 rounded-xl flex items-center justify-center shadow-lg hover-lift group-hover:scale-105 transition-transform duration-200">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-success rounded-full animate-pulse"></div>
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-200">
                  {APP_CONFIG.name}
                </span>
                <span className="text-xs text-muted-foreground hidden sm:block">
                  AI智能职业规划
                </span>
              </div>
            </a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigation.map((item) => (
              <NavLink key={item.href} item={item} currentPath={currentPath} />
            ))}
          </div>

          {/* Search Bar (Desktop) */}
          {showSearch && (
            <div className="hidden md:flex items-center flex-1 max-w-md mx-8" ref={searchRef}>
              <div className="relative w-full">
                <div className={cn(
                  "relative bg-muted/50 rounded-xl border transition-all duration-200",
                  isSearchOpen ? "border-primary shadow-lg" : "border-transparent hover:border-border"
                )}>
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="搜索专业、职业、技能..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onFocus={() => setIsSearchOpen(true)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                    className="pl-10 pr-4 py-2 bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>

                {/* Search Suggestions */}
                {isSearchOpen && (
                  <div className="absolute top-full left-0 right-0 mt-2 bg-card rounded-xl shadow-lg border border-border z-50 animate-slide-down">
                    <div className="p-3">
                      <div className="text-xs font-medium text-muted-foreground mb-2">热门搜索</div>
                      <div className="space-y-1">
                        {['计算机科学', '软件工程师', 'Python编程', '数据分析'].map((suggestion) => (
                          <button
                            key={suggestion}
                            onClick={() => {
                              setSearchQuery(suggestion);
                              handleSearch(suggestion);
                              setIsSearchOpen(false);
                            }}
                            className="w-full text-left px-3 py-2 text-sm text-foreground hover:bg-accent rounded-lg transition-colors"
                          >
                            <Search className="w-3 h-3 inline mr-2 text-muted-foreground" />
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Right Side Actions */}
          <div className="flex items-center space-x-2">
            {/* Mobile Search Button */}
            {showSearch && (
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden hover-scale"
                onClick={() => setIsSearchOpen(!isSearchOpen)}
              >
                <Search className="h-4 w-4" />
              </Button>
            )}

            <ThemeToggle variant="compact" />

            {/* Notifications */}
            <div className="relative">
              <Button variant="ghost" size="icon" className="hover-scale">
                <Bell className="h-4 w-4" />
                {notifications > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-destructive text-destructive-foreground text-xs rounded-full flex items-center justify-center animate-pulse">
                    {notifications}
                  </span>
                )}
              </Button>
            </div>

            {/* User Menu */}
            <div className="relative" ref={userMenuRef}>
              <Button
                variant="ghost"
                size="icon"
                className="hover-scale"
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
              >
                <div className="w-8 h-8 bg-gradient-to-r from-primary to-purple-600 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
              </Button>

              {/* User Dropdown */}
              {isUserMenuOpen && (
                <div className="absolute top-full right-0 mt-2 w-56 bg-card rounded-xl shadow-lg border border-border z-50 animate-slide-down">
                  <div className="p-3 border-b border-border">
                    <div className="font-medium text-foreground">未登录用户</div>
                    <div className="text-sm text-muted-foreground">点击登录获取更多功能</div>
                  </div>
                  <div className="p-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start hover-scale"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <User className="w-4 h-4 mr-2" />
                      登录
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start hover-scale"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      设置
                    </Button>
                  </div>
                  <div className="p-2 border-t border-border">
                    <Button
                      size="sm"
                      className="w-full bg-gradient-to-r from-primary to-purple-600 hover-scale"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      注册账号
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden hover-scale"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden animate-slide-down">
            <div className="px-4 pt-4 pb-6 space-y-4 bg-card/95 backdrop-blur-md rounded-lg mt-2 border border-border shadow-lg">
              {/* Mobile Search */}
              {showSearch && (
                <div className="md:hidden">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      type="text"
                      placeholder="搜索专业、职业、技能..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                      className="pl-10"
                    />
                  </div>
                </div>
              )}

              {/* Navigation Links */}
              <div className="space-y-1">
                {navigation.map((item) => (
                  <MobileNavLink
                    key={item.href}
                    item={item}
                    currentPath={currentPath}
                    onClick={() => setIsMobileMenuOpen(false)}
                  />
                ))}
              </div>

              {/* User Actions */}
              <div className="pt-4 border-t border-border space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">账户</span>
                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <Bell className="h-4 w-4 text-muted-foreground" />
                      {notifications > 0 && (
                        <span className="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full"></span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex flex-col space-y-2">
                  <Button variant="ghost" size="sm" className="justify-start">
                    <User className="w-4 h-4 mr-2" />
                    登录
                  </Button>
                  <Button size="sm" className="bg-gradient-to-r from-primary to-purple-600 justify-start">
                    注册账号
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}

function NavLink({ item, currentPath }: { item: NavItem; currentPath?: string }) {
  const isActive = currentPath === item.href;
  const Icon = item.icon;

  return (
    <div className="relative group">
      <a
        href={item.href}
        className={cn(
          "flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover-scale",
          isActive
            ? "text-primary bg-primary/10 shadow-sm"
            : "text-muted-foreground hover:text-foreground hover:bg-accent"
        )}
      >
        {Icon && <Icon className="w-4 h-4 mr-2" />}
        {item.label}
        {item.badge && (
          <span className={cn(
            "ml-2 px-1.5 py-0.5 text-xs font-medium rounded-full",
            item.badge === 'AI' ? "bg-primary/20 text-primary" : "bg-success/20 text-success"
          )}>
            {item.badge}
          </span>
        )}
      </a>

      {/* Tooltip */}
      {item.description && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap">
          {item.description}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-popover"></div>
        </div>
      )}
    </div>
  );
}

function MobileNavLink({
  item,
  currentPath,
  onClick
}: {
  item: NavItem;
  currentPath?: string;
  onClick: () => void;
}) {
  const isActive = currentPath === item.href;
  const Icon = item.icon;

  return (
    <a
      href={item.href}
      onClick={onClick}
      className={cn(
        "flex items-center px-3 py-3 rounded-lg text-base font-medium transition-all duration-200",
        isActive
          ? "text-primary bg-primary/10 shadow-sm"
          : "text-muted-foreground hover:text-foreground hover:bg-accent"
      )}
    >
      {Icon && <Icon className="w-5 h-5 mr-3" />}
      <div className="flex-1">
        <div className="flex items-center">
          {item.label}
          {item.badge && (
            <span className={cn(
              "ml-2 px-1.5 py-0.5 text-xs font-medium rounded-full",
              item.badge === 'AI' ? "bg-primary/20 text-primary" : "bg-success/20 text-success"
            )}>
              {item.badge}
            </span>
          )}
        </div>
        {item.description && (
          <div className="text-xs text-muted-foreground mt-1">
            {item.description}
          </div>
        )}
      </div>
      <ChevronDown className="w-4 h-4 text-muted-foreground rotate-[-90deg]" />
    </a>
  );
}
