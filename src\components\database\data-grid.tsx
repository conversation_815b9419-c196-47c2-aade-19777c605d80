'use client';

import { ReactNode } from 'react';
import { Loader2, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DataGridProps {
  children: ReactNode;
  loading?: boolean;
  error?: string;
  empty?: boolean;
  emptyMessage?: string;
  emptyIcon?: ReactNode;
  view?: 'grid' | 'list';
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

interface DataGridItemProps {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  href?: string;
}

export function DataGrid({
  children,
  loading = false,
  error,
  empty = false,
  emptyMessage = '暂无数据',
  emptyIcon,
  view = 'grid',
  columns = 3,
  gap = 'md',
  className
}: DataGridProps) {
  const getGridClass = () => {
    if (view === 'list') {
      return 'flex flex-col';
    }
    
    const columnClasses = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6'
    };
    
    return `grid ${columnClasses[columns]}`;
  };

  const getGapClass = () => {
    const gapClasses = {
      sm: 'gap-3',
      md: 'gap-6',
      lg: 'gap-8'
    };
    return gapClasses[gap];
  };

  // 加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertCircle className="w-8 h-8 text-destructive mx-auto mb-4" />
          <p className="text-destructive font-medium mb-2">加载失败</p>
          <p className="text-muted-foreground text-sm">{error}</p>
        </div>
      </div>
    );
  }

  // 空状态
  if (empty) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          {emptyIcon || (
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="w-8 h-8 text-muted-foreground" />
            </div>
          )}
          <p className="text-muted-foreground">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      getGridClass(),
      getGapClass(),
      "animate-fade-in",
      className
    )}>
      {children}
    </div>
  );
}

export function DataGridItem({ 
  children, 
  className, 
  onClick, 
  href 
}: DataGridItemProps) {
  const Component = href ? 'a' : 'div';
  
  return (
    <Component
      href={href}
      onClick={onClick}
      className={cn(
        "group transition-all duration-200 animate-slide-up hover-lift",
        onClick || href ? "cursor-pointer" : "",
        className
      )}
      style={{
        animationDelay: `${Math.random() * 0.3}s`
      }}
    >
      {children}
    </Component>
  );
}

// 骨架屏组件
export function DataGridSkeleton({ 
  columns = 3, 
  rows = 3, 
  gap = 'md' 
}: { 
  columns?: number; 
  rows?: number; 
  gap?: 'sm' | 'md' | 'lg';
}) {
  const getGridClass = () => {
    const columnClasses = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6'
    };
    return `grid ${columnClasses[columns]}`;
  };

  const getGapClass = () => {
    const gapClasses = {
      sm: 'gap-3',
      md: 'gap-6',
      lg: 'gap-8'
    };
    return gapClasses[gap];
  };

  return (
    <div className={cn(getGridClass(), getGapClass())}>
      {Array.from({ length: columns * rows }).map((_, index) => (
        <div key={index} className="bg-card rounded-lg border border-border p-6 animate-pulse">
          <div className="space-y-4">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
            <div className="space-y-2">
              <div className="h-3 bg-muted rounded"></div>
              <div className="h-3 bg-muted rounded w-5/6"></div>
            </div>
            <div className="flex justify-between items-center">
              <div className="h-3 bg-muted rounded w-1/4"></div>
              <div className="h-8 bg-muted rounded w-20"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// 数据统计组件
interface DataStatsProps {
  total: number;
  filtered?: number;
  loading?: boolean;
  className?: string;
}

export function DataStats({ 
  total, 
  filtered, 
  loading = false, 
  className 
}: DataStatsProps) {
  if (loading) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <div className="h-4 bg-muted rounded w-32 animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center space-x-2 text-sm text-muted-foreground", className)}>
      <span>
        {filtered !== undefined && filtered !== total ? (
          <>显示 <span className="font-medium text-foreground">{filtered}</span> 项，共 <span className="font-medium text-foreground">{total}</span> 项</>
        ) : (
          <>共 <span className="font-medium text-foreground">{total}</span> 项</>
        )}
      </span>
    </div>
  );
}
