'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Clock, Eye, ChevronRight, TrendingUp, BookOpen, Briefcase } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { ArticlesAPI } from '@/lib/api/articles';
import { Article } from '@/lib/supabase';

const NEWS_CATEGORIES = [
  { id: 'all', name: '全部', icon: TrendingUp },
  { id: 'policy', name: '政策解读', icon: BookOpen },
  { id: 'industry', name: '行业动态', icon: Briefcase },
  { id: 'education', name: '教育资讯', icon: BookOpen }
];

// 示例新闻数据 - 暂时保留用于后续开发
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const NEWS_DATA = [
  {
    id: 1,
    category: 'policy',
    title: '2024年高考志愿填报新政策解读',
    summary: '教育部发布最新高考志愿填报指导意见，新增专业类别和录取规则调整...',
    image: '/api/placeholder/300/200',
    publishTime: '2024-03-15',
    readCount: 12580,
    isHot: true,
    tags: ['高考政策', '志愿填报']
  },
  {
    id: 2,
    category: 'industry',
    title: '人工智能行业人才需求激增，相关专业成热门',
    summary: 'ChatGPT等AI技术的快速发展，带动了人工智能相关专业的报考热潮...',
    image: '/api/placeholder/300/200',
    publishTime: '2024-03-14',
    readCount: 8960,
    isHot: true,
    tags: ['人工智能', '就业前景']
  },
  {
    id: 3,
    category: 'education',
    title: '双一流高校新增专业盘点',
    summary: '2024年双一流高校新增多个前沿专业，涵盖新能源、生物技术等领域...',
    image: '/api/placeholder/300/200',
    publishTime: '2024-03-13',
    readCount: 6750,
    isHot: false,
    tags: ['双一流', '新专业']
  },
  {
    id: 4,
    category: 'industry',
    title: '新能源汽车产业快速发展，相关专业就业前景广阔',
    summary: '随着碳中和目标的推进，新能源汽车产业迎来黄金发展期...',
    image: '/api/placeholder/300/200',
    publishTime: '2024-03-12',
    readCount: 5420,
    isHot: false,
    tags: ['新能源', '汽车工程']
  },
  {
    id: 5,
    category: 'policy',
    title: '强基计划招生政策调整，理科生迎来新机遇',
    summary: '2024年强基计划在招生专业和选拔方式上进行了重要调整...',
    image: '/api/placeholder/300/200',
    publishTime: '2024-03-11',
    readCount: 4890,
    isHot: false,
    tags: ['强基计划', '理科专业']
  },
  {
    id: 6,
    category: 'education',
    title: '医学专业报考指南：从临床到公共卫生',
    summary: '详解医学类专业的分类、就业方向和报考要求...',
    image: '/api/placeholder/300/200',
    publishTime: '2024-03-10',
    readCount: 7230,
    isHot: false,
    tags: ['医学专业', '报考指南']
  }
];

export function NewsSection() {
  const [activeCategory, setActiveCategory] = useState('all');
  const [articles, setArticles] = useState<Article[]>([]);
  const [filteredArticles, setFilteredArticles] = useState<Article[]>([]);
  const [visibleNews, setVisibleNews] = useState(6);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadArticles();
  }, []);

  const loadArticles = async () => {
    try {
      const result = await ArticlesAPI.searchArticles({ limit: 12 });
      setArticles(result.data);
      setFilteredArticles(result.data);
    } catch (error) {
      console.error('Failed to load articles:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategoryChange = async (categoryId: string) => {
    setActiveCategory(categoryId);
    setIsLoading(true);

    try {
      if (categoryId === 'all') {
        setFilteredArticles(articles);
      } else {
        const result = await ArticlesAPI.searchArticles({
          category: [categoryId],
          limit: 12
        });
        setFilteredArticles(result.data);
      }
    } catch (error) {
      console.error('Failed to filter articles:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const displayedNews = filteredArticles.slice(0, visibleNews);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  const formatReadCount = (count: number) => {
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}万`;
    }
    return count.toString();
  };

  return (
    <section className="py-16">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">今日要闻</h2>
          <p className="text-lg text-gray-600">掌握最新教育动态和行业趋势</p>
        </div>
        
        <Button variant="outline" className="mt-4 md:mt-0">
          查看更多
          <ChevronRight className="w-4 h-4 ml-2" />
        </Button>
      </div>

      {/* 分类标签 */}
      <div className="flex flex-wrap gap-2 mb-8">
        {NEWS_CATEGORIES.map((category) => {
          const Icon = category.icon;
          return (
            <button
              key={category.id}
              onClick={() => handleCategoryChange(category.id)}
              className={cn(
                "flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200",
                activeCategory === category.id
                  ? "bg-blue-600 text-white shadow-lg"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              )}
            >
              <Icon className="w-4 h-4 mr-2" />
              {category.name}
            </button>
          );
        })}
      </div>

      {/* 新闻列表 */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          // 加载骨架屏
          [...Array(6)].map((_, index) => (
            <Card key={index} className="animate-pulse bg-white/60 backdrop-blur-sm border-0">
              <div className="w-full h-48 bg-gray-200 rounded-t-lg" />
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                <div className="h-3 bg-gray-200 rounded w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded" />
                  <div className="h-3 bg-gray-200 rounded w-5/6" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          displayedNews.map((article, index) => (
            <Card
              key={article.id}
              className={cn(
                "group cursor-pointer transition-all duration-300 hover:shadow-xl border-0",
                index === 0 && article.is_hot ? "md:col-span-2 lg:col-span-2" : "",
                "bg-white/60 backdrop-blur-sm"
              )}
              onClick={() => console.log('Navigate to article:', article.id)}
            >
              {/* 新闻图片 */}
              <div className="relative overflow-hidden rounded-t-lg">
                {article.image_url ? (
                  <Image
                    src={article.image_url}
                    alt={article.title}
                    width={400}
                    height={192}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                    <span className="text-gray-500">新闻图片</span>
                  </div>
                )}

                {/* 热门标签 */}
                {article.is_hot && (
                  <div className="absolute top-3 left-3 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                    热门
                  </div>
                )}

                {/* 分类标签 */}
                <div className="absolute top-3 right-3 bg-black/50 text-white text-xs px-2 py-1 rounded-full">
                  {NEWS_CATEGORIES.find(cat => cat.id === article.category)?.name || article.category}
                </div>
              </div>

              <CardHeader className="pb-2">
                <CardTitle className={cn(
                  "font-bold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2",
                  index === 0 && article.is_hot ? "text-xl" : "text-lg"
                )}>
                  {article.title}
                </CardTitle>
                <CardDescription className="text-gray-600 line-clamp-2">
                  {article.summary}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                {/* 标签 */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {article.tags.map((tag, idx) => (
                    <span
                      key={idx}
                      className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* 元信息 */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {formatDate(article.published_at)}
                  </div>
                  <div className="flex items-center">
                    <Eye className="w-4 h-4 mr-1" />
                    {formatReadCount(article.read_count)}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* 加载更多按钮 */}
      {visibleNews < filteredArticles.length && (
        <div className="text-center mt-8">
          <Button 
            variant="outline" 
            onClick={() => setVisibleNews(prev => prev + 6)}
            className="px-8"
          >
            加载更多新闻
          </Button>
        </div>
      )}
    </section>
  );
}
