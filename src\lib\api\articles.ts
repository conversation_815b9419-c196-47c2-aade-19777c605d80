import { supabase, Article } from '@/lib/supabase';

export interface ArticleSearchParams {
  query?: string;
  category?: string[];
  tags?: string[];
  isHot?: boolean;
  limit?: number;
  offset?: number;
}

export class ArticlesAPI {
  static async searchArticles(params: ArticleSearchParams = {}): Promise<{
    data: Article[];
    total: number;
  }> {
    let query = supabase
      .from('articles')
      .select('*', { count: 'exact' });

    // 文本搜索
    if (params.query) {
      query = query.or(`title.ilike.%${params.query}%,summary.ilike.%${params.query}%,tags.cs.{${params.query}}`);
    }

    // 分类筛选
    if (params.category && params.category.length > 0) {
      query = query.in('category', params.category);
    }

    // 标签筛选
    if (params.tags && params.tags.length > 0) {
      query = query.overlaps('tags', params.tags);
    }

    // 热门文章筛选
    if (params.isHot !== undefined) {
      query = query.eq('is_hot', params.isHot);
    }

    // 分页
    if (params.limit) {
      query = query.limit(params.limit);
    }
    if (params.offset) {
      query = query.range(params.offset, params.offset + (params.limit || 10) - 1);
    }

    // 排序：按发布时间倒序
    query = query.order('published_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to search articles: ${error.message}`);
    }

    return {
      data: data || [],
      total: count || 0
    };
  }

  static async getArticleById(id: string): Promise<Article | null> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get article: ${error.message}`);
    }

    // 增加阅读量
    await this.incrementReadCount(id);

    return data;
  }

  static async getHotArticles(limit: number = 10): Promise<Article[]> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('is_hot', true)
      .order('read_count', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get hot articles: ${error.message}`);
    }

    return data || [];
  }

  static async getLatestArticles(limit: number = 10): Promise<Article[]> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get latest articles: ${error.message}`);
    }

    return data || [];
  }

  static async getArticlesByCategory(category: string, limit: number = 10): Promise<Article[]> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('category', category)
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get articles by category: ${error.message}`);
    }

    return data || [];
  }

  static async getRelatedArticles(articleId: string, limit: number = 5): Promise<Article[]> {
    // 首先获取当前文章信息
    const currentArticle = await this.getArticleById(articleId);
    if (!currentArticle) {
      return [];
    }

    // 基于相同分类和相似标签推荐相关文章
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .neq('id', articleId)
      .or(`category.eq.${currentArticle.category},tags.ov.{${currentArticle.tags.join(',')}}`)
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get related articles: ${error.message}`);
    }

    return data || [];
  }

  static async incrementReadCount(articleId: string): Promise<void> {
    const { error } = await supabase.rpc('increment_read_count', {
      article_id: articleId
    });

    if (error) {
      console.error('Failed to increment read count:', error.message);
      // 不抛出错误，因为这不是关键功能
    }
  }

  static async getArticleCategories(): Promise<Array<{ category: string; count: number }>> {
    const { data, error } = await supabase
      .from('articles')
      .select('category')
      .not('category', 'is', null);

    if (error) {
      throw new Error(`Failed to get article categories: ${error.message}`);
    }

    // 统计每个分类的数量
    const categoryCount: Record<string, number> = {};
    data?.forEach(item => {
      categoryCount[item.category] = (categoryCount[item.category] || 0) + 1;
    });

    return Object.entries(categoryCount).map(([category, count]) => ({
      category,
      count
    }));
  }

  static async getMostReadArticles(limit: number = 10): Promise<Article[]> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .order('read_count', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get most read articles: ${error.message}`);
    }

    return data || [];
  }

  static async getArticlesByTag(tag: string, limit: number = 10): Promise<Article[]> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .contains('tags', [tag])
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get articles by tag: ${error.message}`);
    }

    return data || [];
  }

  static async getArticlesByAuthor(author: string, limit: number = 10): Promise<Article[]> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('author', author)
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get articles by author: ${error.message}`);
    }

    return data || [];
  }
}
